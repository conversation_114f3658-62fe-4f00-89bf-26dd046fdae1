package com.payne.auth.security;

import com.payne.core.config.ConfigProperties;
import com.payne.upms.system.service.LoginRecordService;
import com.payne.upms.system.service.SysAccountService;
import jakarta.annotation.Resource;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.annotation.web.configurers.AbstractHttpConfigurer;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;
import org.springframework.security.web.authentication.UsernamePasswordAuthenticationFilter;
import org.springframework.security.web.context.DelegatingSecurityContextRepository;
import org.springframework.security.web.context.HttpSessionSecurityContextRepository;
import org.springframework.security.web.context.RequestAttributeSecurityContextRepository;

/**
 *  2024/8/16.
 */
@Configuration
//@EnableMethodSecurity
@EnableWebSecurity
public class SecurityConfig {
    @Resource
    private JwtAccessDeniedHandler accessDeniedHandler;
    @Resource
    private JwtAuthenticationEntryPoint authenticationEntryPoint;

    @Configuration
    @Profile("dev")
    public static class DevMethodSecurityConfig {

    }

    @EnableMethodSecurity
    @Configuration
    @Profile("!dev")
    public static class PrdMethodSecurityConfig {

    }

    @Bean
    public JwtAuthenticationFilter jwtAuthenticationFilter(ConfigProperties configProperties, LoginRecordService loginRecordService, SysAccountService sysAccountService, RedisTemplate<String, Object> redisTemplate) {
        return new JwtAuthenticationFilter(configProperties, loginRecordService, sysAccountService, redisTemplate);
    }

    @Bean
    public SecurityFilterChain securityFilterChain(HttpSecurity http, JwtAuthenticationFilter jwtAuthenticationFilter) throws Exception {
        return http
                .csrf(AbstractHttpConfigurer::disable)
                .cors(AbstractHttpConfigurer::disable)
                .authorizeHttpRequests(auth -> {
                    auth.requestMatchers("/api/sec_js", "/api/captcha", "/api/login", "/api/file/**",
                            "/api/cus-auth/**", "/api/msg/**", "/api/aigc/chat/**", "/v1/**", "/unified-auth/open/**").permitAll();
                    auth.anyRequest().authenticated();
                })
                .sessionManagement(s -> s.sessionCreationPolicy(SessionCreationPolicy.STATELESS))
                .exceptionHandling(e -> e
                        .authenticationEntryPoint(authenticationEntryPoint)
                        .accessDeniedHandler(accessDeniedHandler))
                .addFilterBefore(jwtAuthenticationFilter, UsernamePasswordAuthenticationFilter.class)
                .securityContext((securityContext) -> securityContext
                        .requireExplicitSave(false)
                        .securityContextRepository(new DelegatingSecurityContextRepository(
                                new RequestAttributeSecurityContextRepository(),
                                new HttpSessionSecurityContextRepository()
                        )))
                .build();
    }

    @Bean
    public BCryptPasswordEncoder bCryptPasswordEncoder() {
        return new BCryptPasswordEncoder();
    }
}
