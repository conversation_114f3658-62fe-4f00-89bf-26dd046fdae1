import request from '@/utils/request';

/**
 * 代码表通用API
 * 用于动态获取各种码表数据，替代前端硬编码
 */

/**
 * 获取指定类型的代码表数据
 * @param {string} codeType 代码表类型 (如: coinType, packType, etc.)
 * @returns {Promise} 代码表数据列表
 */
export async function getCodeList(codeType) {
  const res = await request.get('/code/codeCommon', {
    params: { codeType }
  });
  if (res.data.code === 0) {
    return res.data.data || [];
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取钱币类型选项 (格式化为前端选择器格式)
 * 替代硬编码的 COIN_TYPE_OPTIONS
 * @returns {Promise<Array>} 格式: [{ label: '纸币', value: 'banknote', code: 'banknote' }]
 */
export async function getCoinTypeOptions() {
  try {
    const list = await getCodeList('coinType');
    return list.map(item => ({
      label: item.name,           // 显示名称: 纸币、古钱币等
      value: item.code,           // 值: banknote、ancientCoin等  
      code: item.code,            // 代码: banknote、ancientCoin等
      sort: item.sort,            // 排序
      description: item.description // 描述
    })).sort((a, b) => (a.sort || 0) - (b.sort || 0));
  } catch (error) {
    console.error('获取钱币类型选项失败:', error);
    // 返回默认选项作为后备
    return [
      { label: '纸币', value: 'banknote', code: 'banknote', sort: 1 },
      { label: '古钱币', value: 'ancientCoin', code: 'ancientCoin', sort: 2 },
      { label: '机制币', value: 'machineCoin', code: 'machineCoin', sort: 3 },
      { label: '银锭', value: 'silverIngot', code: 'silverIngot', sort: 4 }
    ];
  }
}

/**
 * 获取钱币类型映射表 (代码 -> 名称)
 * 替代组件中硬编码的类型映射
 * @returns {Promise<Object>} 格式: { banknote: '纸币', ancientCoin: '古钱币' }
 */
export async function getCoinTypeMap() {
  try {
    const list = await getCodeList('coinType');
    const map = {};
    list.forEach(item => {
      map[item.code] = item.name;
    });
    return map;
  } catch (error) {
    console.error('获取钱币类型映射失败:', error);
    // 返回默认映射作为后备
    return {
      banknote: '纸币',
      ancientCoin: '古钱币',
      machineCoin: '机制币',
      silverIngot: '银锭'
    };
  }
}

/**
 * 获取盒子类型选项
 * @returns {Promise<Array>} 
 */
export async function getPackTypeOptions() {
  try {
    const list = await getCodeList('packType');
    return list.map(item => ({
      label: item.name,
      value: item.code,
      sort: item.sort
    })).sort((a, b) => (a.sort || 0) - (b.sort || 0));
  } catch (error) {
    console.error('获取盒子类型选项失败:', error);
    return [];
  }
}

/**
 * 获取品相等级选项  
 * @returns {Promise<Array>}
 */
export async function getGradeOptions() {
  try {
    const list = await getCodeList('grade');
    return list.map(item => ({
      label: item.name,
      value: item.code,
      sort: item.sort
    })).sort((a, b) => (a.sort || 0) - (b.sort || 0));
  } catch (error) {
    console.error('获取品相等级选项失败:', error);
    return [];
  }
}

/**
 * 获取通用选项格式化方法
 * @param {string} codeType 代码表类型
 * @param {Object} options 格式化选项
 * @param {string} options.labelField 标签字段名，默认'name'
 * @param {string} options.valueField 值字段名，默认'code'
 * @returns {Promise<Array>}
 */
export async function getFormattedOptions(codeType, options = {}) {
  const {
    labelField = 'name',
    valueField = 'code'
  } = options;
  
  try {
    const list = await getCodeList(codeType);
    return list.map(item => ({
      label: item[labelField],
      value: item[valueField],
      ...item // 包含原始数据
    })).sort((a, b) => (a.sort || 0) - (b.sort || 0));
  } catch (error) {
    console.error(`获取${codeType}选项失败:`, error);
    return [];
  }
} 