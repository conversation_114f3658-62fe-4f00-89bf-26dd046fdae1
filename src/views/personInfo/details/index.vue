<template>
  <ele-page :multi-card="false">
    <ele-card>
      <div style="margin-bottom: 16px">
        <el-button
          :icon="ArrowLeftOutlined"
          type="primary"
          class="ele-btn-icon"
          @click="onBack"
        >
          返回列表
        </el-button>
      </div>
      <el-descriptions
        :border="true"
        :column="mobile ? 1 : 2"
        class="detail-table"
        v-loading="loading"
      >
        <el-descriptions-item label="工号">
          <div>{{ form.xgh }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="姓名">
          <div>{{ form.xm }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="性别">
          <div>{{ form.xb === '1' ? '男' : form.xb === '2' ? '女' : form.xb }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="出生日期">
          <div>{{ form.csrq }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="手机号">
          <div>{{ form.sjh }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="身份证号">
          <div>{{ form.zjhm }}</div>
        </el-descriptions-item>
        <el-descriptions-item label="邮箱">
          <div>{{ form.email }}</div>
        </el-descriptions-item>

        <!-- 教师特有字段 -->
        <template v-if="userType === 'member'">
          <el-descriptions-item label="公司名称">
            <div>{{ form.deptName }}</div>
          </el-descriptions-item>
        </template>

        <el-descriptions-item label="状态">
          <el-tag
            :type="form.enabled ? 'success' : 'danger'"
            size="small"
            :disable-transitions="true"
          >
            {{ form.enabled ? '启用' : '禁用' }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item label="备注">
          <div>{{ form.bz || '无' }}</div>
        </el-descriptions-item>
      </el-descriptions>
    </ele-card>
  </ele-page>
</template>

<script setup>
import { onActivated, reactive, ref, unref, computed } from 'vue';
import { useRouter } from 'vue-router';
import { EleMessage } from 'ele-admin-plus/es';
import { ArrowLeftOutlined } from '@/components/icons';
import { useMobile } from '@/utils/use-mobile';
import { usePageTab } from '@/utils/use-page-tab';
import { getPersonInfo } from '../api/index';

const ROUTE_PATH = '/personInfo';

const { currentRoute, push } = useRouter();
const { removePageTab, getRouteTabKey, setPageTab } = usePageTab();
const { mobile } = useMobile();

/** 从路由获取userType */
const userType = computed(() => {
  const { params } = unref(currentRoute);
  return params.userType || 'member';
});

/** 用户类型文本 */
const userTypeText = computed(() => {
  return userType.value === 'member' ? '人员' : '用户';
});

/** 人员信息 */
const form = reactive({
  xgh: '',
  xm: '',
  xb: '',
  csrq: '',
  sjh: '',
  zjhm: '',
  email: '',
  enabled: true,
  bz: '',
  deptName: '',
});

/** 请求状态 */
const loading = ref(true);

/** 返回 */
const onBack = () => {
  removePageTab({ key: getRouteTabKey() });
  push(`/personInfo/${userType.value}`);
};

/** 查询详情 */
const query = () => {
  const { params } = unref(currentRoute);
  const xgh = params.xgh;
  if (!xgh) {
    EleMessage.error('工号参数错误');
    return;
  }
  loading.value = true;
  getPersonInfo(userType.value, xgh)
    .then((data) => {
      loading.value = false;
      Object.assign(form, data);
      setPersonTabTitle();
    })
    .catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
};

/** 修改页签标题 */
const setPersonTabTitle = () => {
  if (form.xm && unref(currentRoute).path.startsWith(ROUTE_PATH)) {
    setPageTab({
      key: getRouteTabKey(),
      title: `${userTypeText.value}详情[${form.xm}]`
    });
  }
};

onActivated(() => {
  setPersonTabTitle();
});

query();
</script>

<script>
export default {
  name: 'PersonInfoDetails'
};
</script>

<style lang="scss" scoped>
.detail-table :deep(.el-descriptions__label) {
  width: 108px;
  text-align: right;
  font-weight: normal;
}
</style>
