<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <person-search @search="reload" :user-type="userType" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="xgh"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        :cache-key="`personInfo${userType}Table`"
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            type="danger"
            size="small"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>

          <el-button
            v-if="hasPermission(`personInfo:${userType}:export`)"
            class="ele-btn-icon"
            size="small"
            :icon="DownloadOutlined"
            @click="exportData"
          >
            导出
          </el-button>
        </template>
        <template #xm="{ row }">
          <router-link
            :to="`/personInfo/${userType}/details/${row.xgh}`"
            style="text-decoration: none"
          >
            <el-link type="primary" :underline="false">
              {{ row.xm }}
            </el-link>
          </router-link>
        </template>
        <template #xb="{ row }">
          {{ row.xb === '1' ? '男' : row.xb === '2' ? '女' : row.xb }}
        </template>
        <template #sjh="{ row }">
          {{
            row.sjh
              ? row.sjh.replace(/^(.{3})(?:\w+)(.{4})$/, '$1****$2')
              : ''
          }}
        </template>
        <template #zjhm="{ row }">
          {{
            row.zjhm
              ? row.zjhm.replace(/^(.{6})(?:\w+)(.{4})$/, '$1******$2')
              : ''
          }}
        </template>
        <template #action="{ row }">
          <el-link
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link
            type="primary"
            :underline="false"
            @click="remove(row)"
          >
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <person-edit
      v-model="showEdit"
      :data="current"
      :user-type="userType"
      @done="reload"
    />

    <!-- 修改密码弹窗 -->
    <password-modal
      v-model="passwordVisible"
      :data="current"
      :user-type="userType"
    />
  </ele-page>
</template>

<script setup>
import { ref, computed, unref } from 'vue';
import { useRouter } from 'vue-router';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import {
  DeleteOutlined,
  PlusOutlined,
  UploadOutlined,
  DownloadOutlined
} from '@/components/icons';
import PersonSearch from './components/person-search.vue';
import PersonEdit from './components/person-edit.vue';
import PasswordModal from './components/password-modal.vue';
import { pagePersonInfo, removePersonInfo, exportPersonInfo } from './api/index';
import { usePermission } from '@/utils/use-permission';

const { hasPermission } = usePermission();
const { currentRoute } = useRouter();

/** 从路由中获取userType */
const userType = computed(() => {
  const { params } = unref(currentRoute);
  return params.userType || 'member';
});

/** 表格实例 */
const tableRef = ref(null);

/** 根据userType动态设置列配置 */
const columns = computed(() => {
  const baseColumns = [
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'xgh',
      label: '工号',
      minWidth: 120,
      fixed: 'left'
    },
    {
      prop: 'xm',
      label: '姓名',
      minWidth: 100,
      slot: 'xm',
      fixed: 'left'
    },
    {
      prop: 'xb',
      label: '性别',
      width: 80,
      align: 'center',
      slot: 'xb'
    },
    {
      prop: 'csrq',
      label: '出生日期',
      width: 110,
      align: 'center'
    },
    {
      prop: 'sjh',
      label: '手机号',
      minWidth: 120,
      slot: 'sjh'
    },
    {
      prop: 'zjhm',
      label: '身份证号',
      minWidth: 140,
      slot: 'zjhm'
    },
    {
      prop: 'deptName',
      label: '部门',
      minWidth: 120
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 180,
      align: 'center',
      slot: 'action',
      fixed: 'right'
    }
  ];

  return baseColumns;
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);



/** 是否显示修改密码弹窗 */
const passwordVisible = ref(false);

/** 表格数据源 */
const datasource = ({ page, limit, where, orders, filters }) => {
  return pagePersonInfo(userType.value, { ...where, ...orders, ...filters, page, limit });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  tableRef.value?.reload?.({ page: 1, where });
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};



/** 删除人员信息 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除"' + rows.map((d) => d.xm).join(', ') + '"吗?',
    '系统提示',
    { type: 'warning', draggable: true }
  )
    .then(() => {
      const loading = EleMessage.loading('请求中..');
      removePersonInfo(userType.value, rows.map((d) => d.xgh))
        .then((msg) => {
          loading.close();
          EleMessage.success(msg);
          reload();
        })
        .catch((e) => {
          loading.close();
          EleMessage.error(e.message);
        });
    })
    .catch(() => {});
};

/** 导出数据 */
const exportData = () => {
  const loading = EleMessage.loading('导出中..');
  exportPersonInfo(userType.value, {})
    .then((data) => {
      loading.close();
      const blob = new Blob([data], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `${userType.value === 'member' ? '人员' : '用户'}信息.xlsx`;
      link.click();
      window.URL.revokeObjectURL(url);
    })
    .catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
};
</script>

<script>
export default {
  name: 'PersonInfo'
};
</script>
