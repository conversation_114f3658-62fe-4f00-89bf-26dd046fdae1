import request from '@/utils/request';

/**
 * 查询本人用户信息
 */
export async function getMyInfo() {
    const res = await request.get('/personInfo');
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询人员信息
 */
export async function pagePersonInfo(userType, params) {
    const res = await request.get(`/personInfo/${userType}/queryPage`, {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 根据工号查询人员信息
 */
export async function getPersonInfo(userType, xgh) {
    const res = await request.get(`/personInfo/${userType}/${xgh}`);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加或编辑人员信息
 */
export async function operationPersonInfo(userType, data) {
    const res = await request.post(`/personInfo/${userType}/operation`, data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除人员信息
 */
export async function removePersonInfo(userType, ids) {
    const res = await request.post(`/personInfo/${userType}/remove`, ids);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 导入人员信息
 */
export async function importPersonInfo(userType, file) {
    const formData = new FormData();
    formData.append('file', file);
    const res = await request.post(`/personInfo/${userType}/import`, formData);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 导出人员信息
 */
export async function exportPersonInfo(userType, params) {
    const res = await request.get(`/personInfo/${userType}/export`, {
        params,
        responseType: 'blob'
    });
    return res.data;
}

/**
 * 重置用户密码
 */
export async function resetPersonPassword(userType, data) {
    const res = await request.post(`/personInfo/${userType}/resetPassword`, data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 修改用户状态
 */
export async function updatePersonStatus(userType, data) {
    const res = await request.put(`/personInfo/${userType}/status`, data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
} 