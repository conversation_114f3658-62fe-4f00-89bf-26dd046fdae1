<!-- 人员信息搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form
      size="small"
      label-width="80px"
      @keyup.enter="search"
      @submit.prevent=""
    >
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="工号">
            <el-input
              clearable
              v-model.trim="form.xgh"
              placeholder="请输入工号"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="姓名">
            <el-input
              clearable
              v-model.trim="form.xm"
              placeholder="请输入姓名"
            />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="手机号">
            <el-input
              clearable
              v-model.trim="form.sjh"
              placeholder="请输入手机号"
            />
          </el-form-item>
        </el-col>
        <template v-if="userType === 'member'">
          <el-col :lg="6" :md="12" :sm="12" :xs="24">
            <el-form-item label="公司">
              <el-input
                clearable
                v-model.trim="form.deptName"
                placeholder="请输入公司名称"
              />
            </el-form-item>
          </el-col>
        </template>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label="状态">
            <el-select
              v-model="form.enabled"
              placeholder="请选择状态"
              clearable
              style="width: 100%"
            >
              <el-option label="启用" :value="true" />
              <el-option label="禁用" :value="false" />
            </el-select>
          </el-form-item>
        </el-col>

        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
import { useFormData } from '@/utils/use-form-data';

const emit = defineEmits(['search']);

const props = defineProps({
  userType: {
    type: String,
    default: 'member'
  }
});

/** 表单数据 */
const [form, resetFields] = useFormData({
  xgh: '',
  xm: '',
  sjh: '',
  enabled: undefined,
  deptName: '',
  // 学生字段
  nj: '',
  zymc: '',
  bjmc: '',
  // 教师字段
  bmmc: '',
  zwmc: ''
});

/** 搜索 */
const search = () => {
  emit('search', { ...form });
};

/**  重置 */
const reset = () => {
  resetFields();
  search();
};
</script>
