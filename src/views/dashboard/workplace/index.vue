<template>
  <ele-page class="workplace-page">
    <ele-card class="welcome-card">
      <div class="welcome-header">
        <div class="welcome-title">
          <h1>管理系统</h1>
          <p>管理系统助力您的工作更高效</p>
        </div>
      </div>
    </ele-card>

    <!-- 最新动态区域 -->
    <ele-card class="news-card" title="平台动态">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="index"
          :timestamp="activity.timestamp"
          :type="activity.type"
        >
          {{ activity.content }}
        </el-timeline-item>
      </el-timeline>
    </ele-card>
  </ele-page>
</template>

<script setup>
import { ref } from 'vue';
import {
  ChatDotRound,
  EditPen,
  DataLine,
  Tools
} from '@element-plus/icons-vue';

const aiServices = ref([
  {
    id: 1,
    title: '智能对话',
    description: '基于先进的语言模型,为您提供智能、精准的对话服务',
    icon: ChatDotRound
  },
]);

// 平台动态数据
const activities = ref([
  {
    content: '平台正式上线,欢迎您的加入!',
    timestamp: '2025-05-01',
    type: 'success'
  },
]);
</script>

<style lang="scss" scoped>
.workplace-page {
  .welcome-card {
    margin-bottom: 20px;

    .welcome-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px;

      @media (max-width: 768px) {
        flex-direction: column;
        text-align: center;
      }
    }

    .welcome-title {
      h1 {
        font-size: 28px;
        color: #303133;
        margin: 0 0 10px 0;
      }

      p {
        font-size: 16px;
        color: #909399;
        margin: 0;
      }
    }

    .welcome-stats {
      display: flex;
      gap: 40px;

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 24px;
          font-weight: bold;
          color: #409EFF;
        }

        .stat-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }

  .service-card {
    height: 100%;
    text-align: center;
    margin-bottom: 20px;
    transition: all 0.3s;

    &:hover {
      transform: translateY(-5px);
      box-shadow: 0 2px 12px 0 rgba(0,0,0,0.1);
    }

    .service-icon {
      color: #409EFF;
      margin-bottom: 15px;
    }

    h3 {
      margin: 10px 0;
      color: #303133;
    }

    p {
      color: #909399;
      margin-bottom: 20px;
      min-height: 40px;
    }
  }

  .news-card {
    margin-top: 20px;
  }
}
</style>
