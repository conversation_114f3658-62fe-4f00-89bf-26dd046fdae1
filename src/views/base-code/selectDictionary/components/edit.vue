<template>
  <ele-drawer
    :size="530"
    :title="isUpdate ? '修改信息' : '新建信息'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <pro-form
      ref="formRef"
      size="small"
      :model="form"
      :items="items"
      :grid="{ span: 24 }"
      labelWidth="auto"
      @updateValue="setFieldValue"
    >
    </pro-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        type="primary"
        size="small"
        plain
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data.js';
  import { EleMessage } from 'ele-admin-plus';
  import { operation } from '../api/index.js';
  import ProForm from '@/components/ProForm/index.vue';

  const BASE_URL = import.meta.env.BASE_URL;
  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 表单实例 */
  const formRef = ref(null);
  /** 表单数据 */
  const [form, resetFields, assignFields, setFieldValue] = useFormData({
    id: void 0,
    name: '',
    url: '',
    textField: '',
    valueField: '',
    sort: ''
  });

  /** 表单项 */
  const items = ref([
    { prop: 'name', label: '名称', type: 'input', required: true },
    { prop: 'url', label: '接口地址', type: 'input', required: true },
    { prop: 'textField', label: 'textField', type: 'input' },
    { prop: 'valueField', label: 'valueField', type: 'input' },
    { prop: 'sort', label: '排序', type: 'inputNumber' }
  ]);

  /** 提交状态 */
  const loading = ref(false);
  /** 提交 */
  const onSubmit = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      // let data = toFormData({...form})
      loading.value = true;
      operation(form)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          assignFields({
            ...props.data
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
