import request from '@/utils/request';

/**
 * 导入
 */
export async function importData(file, codeType) {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('codeType', codeType);
    const res = await request.post('/code/codeCommon/importData', formData);
    if (res.data.code === 0 || res.data.code === 2) {
        return res.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 获取codeType
 */
// export async function getCodeType(params) {
//     const res = await request.get('/code/codeCommon/codeType', {params});
//     if (res.data.code === 0) {
//         return res.data.data;
//     }
//     return Promise.reject(new Error(res.data.message));
// }
/**
 * 查询不分页
 */
export async function getCodeData(params) {
    const res = await request.get('/code/codeCommon', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/code/codeCommon/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 添加/修改 (统一调用operation接口)
 */
export async function add(data, codeType) {
    const res = await request.post('/code/codeCommon/operation?codeType=' + codeType, data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 修改 (统一调用operation接口)
 */
export async function update(data, codeType) {
    const res = await request.post('/code/codeCommon/operation?codeType=' + codeType, data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.delete('/code/codeCommon/batch', {
        data
    });
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询代码表数据列表 (别名函数，用于兼容性)
 * @param {Object} params 查询参数，包含 codeType 等
 * @returns {Promise} 代码表数据列表
 */
export async function list(params) {
    return getCodeData(params);
}
