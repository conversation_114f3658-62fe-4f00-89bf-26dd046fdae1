<!-- 搜索表单 -->
<template>
  <ele-card :body-style="{ padding: '16px 0 0px 0px !important' }">
    <el-form label-width="72px" @keyup.enter="search" @submit.prevent="">
      <el-row :gutter="8">
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item :label="codeTypeFormat + '代码'">
            <el-input clearable v-model.trim="form.code" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item :label="codeTypeFormat + '名称'">
            <el-input clearable v-model.trim="form.name" placeholder="请输入" />
          </el-form-item>
        </el-col>
        <el-col :lg="6" :md="12" :sm="12" :xs="24">
          <el-form-item label-width="16px">
            <el-button type="primary" @click="search">查询</el-button>
            <el-button @click="reset">重置</el-button>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </ele-card>
</template>

<script setup>
  import { ref, unref, watch } from 'vue';
  import { useFormData } from '@/utils/use-form-data';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';

  const userStore = useUserStore();
  const { codeType } = storeToRefs(userStore);

  let codeTypeFormat = ref(null);
  const { currentRoute } = useRouter();
  watch(
    currentRoute,
    (route) => {
      const { params } = unref(route);
      codeTypeFormat = codeType.value[params.codeType];
    },
    { immediate: true }
  );

  const emit = defineEmits(['search']);

  /** 表单数据 */
  const [form, resetFields] = useFormData({
    code: '',
    name: ''
  });

  /** 搜索 */
  const search = () => {
    emit('search', { ...form });
  };

  /**  重置 */
  const reset = () => {
    resetFields();
    search();
  };
</script>
