<!-- 编辑弹窗 -->
<template>
  <ele-drawer
    :size="430"
    :title="(isUpdate ? '修改' : '添加') + codeTypeFormat"
    :append-to-body="true"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="80px"
      @submit.prevent=""
    >
      <el-form-item label="代码" prop="code">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.code"
          placeholder="请输入代码"
        />
      </el-form-item>
      <el-form-item label="名称" prop="name">
        <el-input
          clearable
          :maxlength="20"
          v-model="form.name"
          placeholder="请输入名称"
        />
      </el-form-item>

      <el-form-item label="排序号" prop="sort">
        <el-input-number
          :min="0"
          :max="99999"
          v-model="form.sort"
          placeholder="请输入排序号"
          controls-position="right"
          class="ele-fluid"
        />
      </el-form-item>
      <el-form-item label="备注">
        <el-input
          :rows="6"
          type="textarea"
          show-word-limit
          maxlength="255"
          v-model="form.description"
          placeholder="请输入备注"
        />
      </el-form-item>
      <el-form-item label="附件">
        <FileUpload
          v-model="form.attachment"
          @changeDelIds="changeDelIds"
          :limit="10"
          accept=".doc,.docx,.pdf,.xls,.xlsx,.txt,.jpg,.png"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" :loading="loading" @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, unref, watch } from 'vue';
  import { EleMessage } from 'ele-admin-plus/es';
  import { useFormData } from '@/utils/use-form-data';
  import { add, update } from '../api/index';
  import { useRouter } from 'vue-router';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import FileUpload from '@/components/FileUpload/index.vue';
  import { isImageFile, toFormData } from '@/utils/common';

  const userStore = useUserStore();
  const { codeType } = storeToRefs(userStore);

  let codeTypeFormat = ref(null);
  let routerParams = ref(null);
  const { currentRoute } = useRouter();
  watch(
    currentRoute,
    (route) => {
      const { params } = unref(route);
      routerParams = params;
      codeTypeFormat = codeType.value[params.codeType];
    },
    { immediate: true }
  );

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object
  });

  /** 是否是修改 */
  const isUpdate = ref(false);

  /** 提交状态 */
  const loading = ref(false);

  /** 表单实例 */
  const formRef = ref(null);

  /** 表单数据 */
  const [form, resetFields, assignFields] = useFormData({
    id: void 0,
    code: '',
    name: '',
    sort: void 0,
            description: '',
    attachment: []
  });

  /** 要删除的附件ID，字符串逗号分割*/
  const deleteFileIds = ref(null);

  /** 表单验证规则 */
  const rules = reactive({
    code: [
      {
        required: true,
        message: '请输入代码',
        type: 'string',
        trigger: 'blur'
      }
    ],
    name: [
      {
        required: true,
        message: '请输入名称',
        type: 'string',
        trigger: 'blur'
      }
    ]
  });

  /** 保存编辑 */
  const save = () => {
    formRef.value?.validate?.((valid) => {
      if (!valid) {
        return;
      }
      loading.value = true;
      
      // 处理文件上传
      let fileArray = [];
      if (form.attachment.length > 0) {
        form.attachment.forEach((e) => {
          if (e.status !== 'done') {
            fileArray.push(e);
          }
        });
      }
      
      let data = toFormData({
        ...form,
        attachment: fileArray,
        deleteFileIds: deleteFileIds.value,
        codeType: routerParams.codeType
      });
      
      const saveOrUpdate = isUpdate.value ? update : add;
      saveOrUpdate(data, routerParams.codeType)
        .then((msg) => {
          loading.value = false;
          EleMessage.success(msg);
          updateModelValue(false);
          emit('done');
        })
        .catch((e) => {
          loading.value = false;
          EleMessage.error(e.message);
        });
    });
  };

  /** changeDelIds */
  const changeDelIds = (value) => {
    deleteFileIds.value = value;
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          const oldFiles = props.data.attachment
            ? JSON.parse(props.data.attachment).map((d, i) => {
                let newObj = {
                  key: d.id,
                  name: d.originalFilename,
                  contentType: d.contentType,
                  fileUrl: import.meta.env.BASE_URL + 'api/file/inline/' + d.id,
                  status: 'done'
                };
                let mark = isImageFile(newObj);
                newObj.isImageFile = mark;
                if (mark) newObj.url = newObj.fileUrl;
                return newObj;
              })
            : [];
          assignFields({
            ...props.data,
            attachment: oldFiles
          });
          isUpdate.value = true;
        } else {
          isUpdate.value = false;
        }
      } else {
        resetFields();
        formRef.value?.clearValidate?.();
      }
    }
  );
</script>
