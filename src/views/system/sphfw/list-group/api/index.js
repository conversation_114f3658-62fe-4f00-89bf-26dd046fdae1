import request from '@/utils/request';


/**
 * 根据id查询用户信息组
 */
export async function getListGroupConfigById(id) {
    const res = await request.get('/dictionary/listGroupConfig/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getListGroupConfig(params) {
    const res = await request.get('/dictionary/listGroupConfig', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/dictionary/listGroupConfig/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/dictionary/listGroupConfig/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/dictionary/listGroupConfig/remove', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
