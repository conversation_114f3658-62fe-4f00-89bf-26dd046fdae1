import request from '@/utils/request';


/**
 * 根据id查询
 */
export async function getDictionaryFieldLinkById(id) {
    const res = await request.get('/dictionary/dictionaryFieldLink/' + id);
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}

/**
 * 查询不分页
 */
export async function getDictionaryFieldLink(params) {
    const res = await request.get('/dictionary/dictionaryFieldLink', {params});
    if (res.data.code === 0 && res.data.data) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 分页查询
 */
export async function queryPage(params) {
    const res = await request.get('/dictionary/dictionaryFieldLink/page', {params});
    if (res.data.code === 0) {
        return res.data.data;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 添加&修改
 */
export async function operation(data) {
    const res = await request.post('/dictionary/dictionaryFieldLink/operation', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}


/**
 * 批量删除
 */
export async function removes(data) {
    const res = await request.post('/dictionary/dictionaryFieldLink/removeByFieldId', data);
    if (res.data.code === 0) {
        return res.data.message;
    }
    return Promise.reject(new Error(res.data.message));
}
