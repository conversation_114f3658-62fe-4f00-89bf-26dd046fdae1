<template>
  <ele-drawer
    :size="930"
    title="设置字段显示隐藏"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    @update:modelValue="updateModelValue"
  >
    <!--        datasource== {{datasource}}<br/><br/>-->
    <!--        columns== {{columns}}<br/>-->
    <el-form
      ref="formRef"
      :model="datasource"
      label-width="0px"
      @submit.prevent=""
      size="small"
    >
      <div style="overflow: auto">
        <ele-table
          style="min-width: 580px; table-layout: fixed; text-align: left"
          border
        >
          <thead>
            <tr>
              <template v-for="header in columns">
                <th :style="{ width: header.width + 'px' }">{{
                  header.label
                }}</th>
              </template>
            </tr>
          </thead>
          <tbody>
            <tr v-for="(item, iindex) in datasource" :key="item.prop">
              <template v-for="(row, rindex) in columns" :key="rindex">
                <td
                  v-if="item.nameRowSpan > 0 && rindex === 0"
                  :rowspan="item.nameRowSpan"
                >
                  {{ item[row.prop] }}
                </td>
                <td
                  v-else-if="item.nameRowSpan === 0 && rindex === 0"
                  :rowspan="item.nameRowSpan"
                  style="display: none"
                >
                  {{ item[row.prop] }}
                </td>
                <td v-else>
                  <template
                    v-if="
                      row.prop === 'linkFieldDataType' ||
                      row.prop === 'showFlag'
                    "
                  >
                    <TableFormItem
                      :item="row"
                      :model="item"
                      :rules="rules"
                      @updateValue="
                        (value) => updateValue(iindex, row.prop, value)
                      "
                    >
                      <template
                        v-for="name in Object.keys($slots).filter(
                          (k) =>
                            ![
                              'default',
                              'footer',
                              'topExtra',
                              'bottomExtra'
                            ].includes(k)
                        )"
                        #[name]="slotProps"
                      >
                        <slot :name="name" v-bind="slotProps || {}"></slot>
                      </template>
                    </TableFormItem>
                    <el-input
                      v-if="item[row.linkFieldDataType] === 'other'"
                      clearable
                      :maxlength="20"
                      v-model="row.linkFieldDataUrl"
                      placeholder="请输入"
                    />
                  </template>
                  <div v-else class="editable-cell-text">{{
                    item[row.prop]
                  }}</div>
                </td>
              </template>
            </tr>
            <tr v-if="!datasource || !datasource.length">
              <td :colspan="columns.length" style="text-align: center">
                <ele-text style="padding: 4px 0" type="secondary">
                  暂无数据
                </ele-text>
              </td>
            </tr>
          </tbody>
        </ele-table>
      </div>
    </el-form>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="onSubmit"
      >
        提交
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { reactive, ref, watch } from 'vue';
  import { getCodeData } from '@/views/base-code/dictionary/api/data-index';
  import { getDicFieldValueByUrl, getDictionaryField } from '../api/index';
  import { getDictionaryFieldLink, operation } from '../api/field-link-index';
  import { generateRandomString } from '@/utils/common';
  import { EleMessage } from 'ele-admin-plus';
  import TableFormItem from '@/components/ProForm/components/table-form-item.vue';

  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /** 修改回显的数据 */
    data: Object,
    userType: String
  });

  /** 表格数据源 */
  const datasource = ref([]);

  /** 表格列配置 */
  const columns = ref([
    {
      columnKey: 'fieldVal',
      prop: 'fieldVal',
      label: '选项'
    },
    {
      prop: 'linkField',
      label: '英文名'
    },
    {
      prop: 'fieldZh',
      label: '中文名'
    },
    {
      prop: 'sfbt',
      label: '原必填值'
    },
    {
      prop: 'showFlag',
      label: '关联字段是否显示',
      type: 'switchSf'
      // type: 'dictSelect',
      // props: {
      //   code: 'yesNo',
      //   dicQueryParams: {
      //     getValType: 'name'
      //   },
      // },
      // slot: 'showFlag',
    },
    {
      prop: 'linkFieldDataType',
      label: '联动表达式',
      // slot: 'linkFieldDataType',
      width: 260,
      type: 'dictSelect',
      props: { code: 'listDicCode', filterable: true }
    }
  ]);
  /** 提交状态 */
  const loading = ref(false);
  /** 表单数据 */
  const form = reactive({
    users: []
  });
  let tableRowEditId = ref(null); // 控制可编辑的每一行
  let tableColumnEditIndex = ref(null); //控制可编辑的每一列

  /** 更新值 */
  const updateValue = (index, prop, value) => {
    datasource.value[index][prop] = value;
  };

  const onSubmit = () => {
    const data = datasource.value;
    loading.value = true;
    let postMethod = operation;
    console.log(data);
    postMethod(data)
      .then((msg) => {
        loading.value = false;
        EleMessage.success(msg);
        updateModelValue(false);
        emit('done');
      })
      .catch((e) => {
        loading.value = false;
        EleMessage.error(e.message);
      });
  };

  const getDataSource = async () => {
    let dictionaryFieldLinkData = [];
    await getDictionaryFieldLink({ fieldId: props.data.fieldId }).then(
      (arr) => {
        dictionaryFieldLinkData = arr;
      }
    );
    let newObj = {
      groupId: props.data.groupId,
      currentLinkFieldEn: props.data.fieldEn,
      userType: props.userType,
      // required: '否',
      showFlag: '否',
      sort: 'sort',
      order: 'desc'
    };
    let dictionaryField = [];
    let codeData = [];

    await getDictionaryField(newObj).then((arr) => {
      dictionaryField = arr;
    });
    if (props.data.loadDataType === 'other') {
      if (
        !['/code/codeBjb', '/code/codeZyb', '/code/codeDwb'].includes(
          props.data.loadDataUrl
        )
      ) {
        await getDicFieldValueByUrl(props.data.loadDataUrl).then((arr) => {
          codeData = arr;
        });
      }
    } else {
      await getCodeData({
        sort: 'sort',
        order: 'as',
        codeType: props.data.loadDataType
      }).then((arr) => {
        codeData = arr;
      });
    }
    let fjtjObjs = [];
    for (var i = 0; i < dictionaryField.length; i++) {
      var arr = dictionaryField[i];
      for (var j = 0; j < codeData.length; j++) {
        var row1 = codeData[j];
        let fjtj = {
          key: generateRandomString(10),
          nameRowSpan: i === 0 ? dictionaryField.length : 0,
          fieldId: props.data.fieldId,
          fieldEn: props.data.fieldEn,
          fieldVal: row1.name,
          fieldValId: row1.id,
          showFlag: '否',
          linkFieldDataType: '',
          linkFieldDataUrl: '',
          linkField: arr.fieldEn,
          fieldZh: arr.fieldZh,
          sfbt: arr.required,
          groupId: props.data.groupId
        };
        fjtjObjs.push(fjtj);
      }
    }
    // 根据group属性进行分组排序，并在每个group内按value升序排序
    const sortedItems = fjtjObjs.sort((a, b) => {
      if (a.fieldVal < b.fieldVal) return -1;
      if (a.fieldVal > b.fieldVal) return 1;
    });

    sortedItems.forEach((e) => {
      e.userType = props.userType;
      let linkData = dictionaryFieldLinkData?.filter?.((d) => {
        return d.fieldVal === e.fieldVal && d.linkField === e.linkField;
      });
      if (linkData.length > 0) {
        e.showFlag = linkData[0].showFlag;
        e.linkFieldDataType = linkData[0].linkFieldDataType;
        e.linkFieldDataUrl = linkData[0].linkFieldDataUrl;
      }
    });
    datasource.value = sortedItems;
  };


  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };
  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        if (props.data) {
          if (props.data.infoType) {
            queryFormTemplateField();
          } else {
            getDataSource();
          }
        }
      }
    },
    {
      immediate: true
    }
  );
</script>

<style lang="scss" scoped></style>
