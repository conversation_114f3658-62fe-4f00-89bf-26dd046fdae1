import request from '@/utils/request';

/**
 * 获取数据库表列表
 */
export async function getTables(params) {
  const { dataSource, ...otherParams } = params;
  const res = await request.get('/generator/tables', {
    params: {
      ...dataSource,
      ...otherParams
    }
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取表字段信息
 */
export async function getColumns(params) {
  const { dataSource, ...otherParams } = params;
  const res = await request.get('/generator/columns', {
    params: {
      ...dataSource,
      ...otherParams
    }
  });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 分页查询表信息
 */
export async function page(params) {
  const res = await request.get('/generator/tables', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 生成代码
 */
export async function generate(data) {
  const res = await request.post('/generator/generate', data, {
    responseType: data.generateType === 2 ? 'blob' : 'json'
  });
  
  if (data.generateType === 2) {
    return res.data;
  }
  
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取数据源列表
 */
export async function listDataSources(params) {
  const res = await request.get('/datasource/list', { params });
  if (res.data.code === 0 && res.data.data) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
