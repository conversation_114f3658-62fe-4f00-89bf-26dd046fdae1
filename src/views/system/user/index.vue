<template>
  <ele-page flex-table>
    <!-- 搜索表单 -->
    <user-search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <!-- 表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        cache-key="systemUserTable"
      >
        <template #toolbar>
          <el-button
            v-if="hasPermission('system:account:operation')"
            type="primary"
            size="small"
            :icon="PlusOutlined"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            v-if="hasPermission('system:account:remove')"
            type="danger"
            size="small"
            :icon="DeleteOutlined"
            @click="remove()"
          >
            删除
          </el-button>
          <el-button
            v-if="hasPermission('code:codeCommon:import')"
            class="ele-btn-icon"
            size="small"
            :icon="UploadOutlined"
            @click="openImport"
          >
            导入
          </el-button>
        </template>
        <template #realName="{ row }">
          <router-link
            :to="'/system/user/details/' + row.userId"
            style="text-decoration: none"
          >
            <el-link type="primary" :underline="false">
              {{ row.realName }}
            </el-link>
          </router-link>
        </template>
        <template #roleName="{ row }">
          <el-tag
            v-for="item in row.roleName.split(',')"
            :key="item"
            size="small"
            :disable-transitions="true"
            style="margin-right: 6px"
          >
            {{ item }}
          </el-tag>
        </template>
        <template #idCode="{ row }">
          {{
            row.idCode
              ? row.idCode.replace(/^(.{6})(?:\w+)(.{4})$/, '\$1******\$2')
              : ''
          }}
        </template>
        <template #telMobile="{ row }">
          {{
            row.telMobile
              ? row.telMobile.replace(/^(.{3})(?:\w+)(.{4})$/, '\$1****\$2')
              : ''
          }}
        </template>
        <template #accountNonExpired="{ row }">
          {{ row.accountNonExpired ? '否' : '是' }}
        </template>
        <template #accountNonLocked="{ row }">
          {{ row.accountNonLocked ? '否' : '是' }}
        </template>
        <template #credentialsNonExpired="{ row }">
          {{ row.credentialsNonExpired ? '是' : '否' }}
        </template>
        <template #enabled="{ row }">
          {{ row.enabled ? '是' : '否' }}
        </template>
        <template #action="{ row }">
          <el-link type="primary" :underline="false" @click="openEdit(row)">
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="resetPsw(row)">
            重置密码
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>
    <!-- 编辑弹窗 -->
    <user-edit v-model="showEdit" :data="current" @done="reload" />
    <!-- 导入弹窗 -->
    <user-import v-model="showImport" @done="reload" />
    <!-- 修改密码弹窗 -->
    <password-modal v-model="passwordVisible" :data="current" />
  </ele-page>
</template>

<script setup>
  import { ref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage } from 'ele-admin-plus/es';
  import {
    DeleteOutlined,
    PlusOutlined,
    UploadOutlined
  } from '@/components/icons';
  import UserSearch from './components/user-search.vue';
  import UserEdit from './components/user-edit.vue';
  import UserImport from './components/user-import.vue';
  import PasswordModal from './components/password-modal.vue';
  import { pageUsers, removeUsers } from './api/index';
  import { usePermission } from '@/utils/use-permission';

  const { hasPermission } = usePermission();

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      type: 'selection',
      columnKey: 'selection',
      width: 50,
      align: 'center',
      fixed: 'left'
    },
    {
      prop: 'username',
      label: '用户账号',
      minWidth: 90
    },
    {
      prop: 'realName',
      label: '用户名',
      minWidth: 90,
      slot: 'realName'
    },
    {
      prop: 'gender',
      label: '性别',
      sortable: 'custom',
      width: 90,
      align: 'center',
      columnKey: 'gender',
      filters: [
        { text: '男', value: '男' },
        { text: '女', value: '女' }
      ],
      filterMultiple: false
    },
    {
      prop: 'telMobile',
      label: '手机号',
      minWidth: 90,
      slot: 'telMobile'
    },
    {
      prop: 'deptName',
      label: '所属公司',
      sortable: 'custom',
      minWidth: 90,
      align: 'center',
    },
    {
      prop: 'idType',
      label: '身份类型',
      minWidth: 90,
      slot: 'idType'
    },
    {
      prop: 'idCode',
      label: '身份号码',
      minWidth: 100,
      slot: 'idCode'
    },
    {
      columnKey: 'roleName',
      label: '角色',
      minWidth: 140,
      slot: 'roleName'
    },
    /*{
      prop: 'accountNonExpired',
      label: '账户是否过期',
      width: 120,
      align: 'center',
      sortable: 'custom',
      slot: 'accountNonExpired'
    },
    {
      prop: 'accountNonLocked',
      label: '是否锁定',
      width: 120,
      align: 'center',
      sortable: 'custom',
      slot: 'accountNonLocked'
    },
    {
      prop: 'credentialsNonExpired',
      label: '密码是否过期',
      width: 130,
      align: 'center',
      sortable: 'custom',
      slot: 'credentialsNonExpired'
    },
    {
      prop: 'enabled',
      label: '是否启用',
      width: 90,
      align: 'center',
      sortable: 'custom',
      slot: 'enabled'
    },*/

    {
      columnKey: 'action',
      label: '操作',
      width: 170,
      align: 'left',
      slot: 'action',
      fixed: 'right'
    }
  ]);

  /** 表格选中数据 */
  const selections = ref([]);

  /** 当前编辑数据 */
  const current = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 是否显示用户导入弹窗 */
  const showImport = ref(false);
  /** 是否显示修改密码弹窗 */
  const passwordVisible = ref(false);

  /** 表格数据源 */
  const datasource = ({ page, limit, where, orders, filters }) => {
    return pageUsers({ ...where, ...orders, ...filters, page, limit });
  };

  /** 搜索 */
  const reload = (where) => {
    selections.value = [];
    tableRef.value?.reload?.({ page: 1, where });
  };

  /** 打开编辑弹窗 */
  const openEdit = (row) => {
    current.value = row ?? null;
    showEdit.value = true;
  };

  /** 打开编辑弹窗 */
  const openImport = () => {
    showImport.value = true;
  };

  /** 删除用户 */
  const remove = (row) => {
    const rows = row == null ? selections.value : [row];
    if (!rows.length) {
      EleMessage.error('请至少选择一条数据');
      return;
    }
    ElMessageBox.confirm(
      '确定要删除“' + rows.map((d) => d.realName).join(', ') + '”吗?',
      '系统提示',
      { type: 'warning', draggable: true }
    )
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removeUsers(rows.map((d) => d.id))
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            reload();
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  /** 重置用户密码 */
  const resetPsw = (row) => {
    passwordVisible.value = true;
    current.value = row ?? null;
  };
</script>

<script>
  export default {
    name: 'SystemUser'
  };
</script>
