<template>
  <ele-page flex-table>
    <menu-search @search="searchReload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 5px 10px 5px!important', overflow: 'hidden' }"
    >
      <ele-pro-table
        sticky
        ref="tableRef"
        row-key="menuId"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        :lazy="true"
        :load="tableLoad"
        highlight-current-row
      >
        <template #toolbar>
          <el-button
            type="primary"
            size="small"
            class="ele-btn-icon"
            :icon="PlusOutlined"
            @click="openEdit(null, 'add')"
          >
            新建
          </el-button>
        </template>
        <template #title="{ row }">
          <el-icon
            v-if="row.icon"
            :size="15"
            style="margin-right: 8px; vertical-align: -2px"
          >
            <component :is="row.icon" />
          </el-icon>
          <span>{{ row.title }}</span>
        </template>
        <template #menuType="{ row }">
          <el-tag
            v-if="isExternalLink(row.path)"
            size="small"
            type="danger"
            :disable-transitions="true"
          >
            外链
          </el-tag>
          <el-tag
            v-else-if="isExternalLink(row.component)"
            size="small"
            type="warning"
            :disable-transitions="true"
          >
            内链
          </el-tag>
          <el-tag
            v-else-if="row.menuType === 0"
            size="small"
            :disable-transitions="true"
          >
            目录
          </el-tag>
          <el-tag
            v-else-if="row.menuType === 1"
            size="small"
            type="success"
            :disable-transitions="true"
          >
            菜单
          </el-tag>
          <el-tag
            v-else-if="row.menuType === 2"
            size="small"
            type="info"
            :disable-transitions="true"
          >
            按钮
          </el-tag>
        </template>
        <template #action="{ row }">
          <el-link
            v-if="row.hasChildren"
            type="primary"
            :underline="false"
            @click="openEdit(row, 'add')"
          >
            添加
          </el-link>
          <el-divider v-if="row.hasChildren" direction="vertical" />
          <el-link
            type="primary"
            :underline="false"
            @click="openEdit(row, 'edit')"
          >
            修改
          </el-link>
          <el-divider direction="vertical" />
          <el-link type="primary" :underline="false" @click="remove(row)">
            删除
          </el-link>
          <template v-if="row.hasChildren">
            <el-divider direction="vertical" />
            <ele-dropdown
              :items="optItems(row)"
              @command="(key) => dropClick(key, row)"
            >
              <el-link type="primary" :underline="false">
                <span>更多</span>
                <el-icon :size="12">
                  <ArrowDown />
                </el-icon>
              </el-link>
            </ele-dropdown>
          </template>
          <!--                    <el-divider direction="vertical" v-if="row.hasChildren"/>-->
          <!--                    <el-link v-if="row.hasChildren" type="primary" :underline="false" @click="reloadChild(row)">刷新子级-->
          <!--                    </el-link>-->
          <!--                    <el-divider direction="vertical" v-if="row.hasChildren"/>-->
          <!--                    <el-link v-if="row.hasChildren" type="primary" :underline="false" @click="reloadParent(row)">刷新父级-->
          <!--                    </el-link>-->
        </template>
      </ele-pro-table>
    </ele-card>
    <menu-edit
      v-model="showEdit"
      :data="current"
      :parent-id="parentId"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
  import { ref, unref } from 'vue';
  import { ElMessageBox } from 'element-plus/es';
  import { EleMessage, isExternalLink } from 'ele-admin-plus/es';
  import { PlusOutlined } from '@/components/icons';
  import MenuSearch from './components/menu-search.vue';
  import MenuEdit from './components/menu-edit.vue';
  import { listMenus, pageMenus, removeMenu } from '@/api/system/menu';
  import { useUserStore } from '@/store/modules/user.js';
  import { useRouter } from 'vue-router';

  const userStore = useUserStore();
  const { currentRoute, push } = useRouter();
  const { path } = unref(currentRoute);
  let routeType = path.split('/')[3];
  console.log(routeType);

  /** 表格实例 */
  const tableRef = ref(null);

  /** 表格列配置 */
  const columns = ref([
    {
      prop: 'title',
      label: '菜单名称',
      slot: 'title',
      minWidth: 130
    },
    {
      prop: 'path',
      label: '路由地址',
      minWidth: 110
    },
    {
      prop: 'sortNumber',
      label: '排序',
      width: 80,
      align: 'center',
      sortable: 'custom'
    },
    {
      prop: 'hide',
      label: '可见',
      width: 80,
      align: 'center',
      formatter: (row) => ['是', '否'][row.hide]
    },
    {
      prop: 'menuType',
      label: '类型',
      width: 80,
      align: 'center',
      slot: 'menuType'
    },
    {
      columnKey: 'action',
      label: '操作',
      width: 230,
      align: 'left',
      slot: 'action'
    }
  ]);

  /** 当前编辑数据 */
  const current = ref(null);
  const currentClick = ref(null);

  /** 是否显示编辑弹窗 */
  const showEdit = ref(false);

  /** 上级菜单id */
  const parentId = ref();

  /** 表格数据源 */
  const datasource = async ({ page, limit, where, orders, parent }) => {
    //默认排序
    orders =
      Object.keys(orders).length > 0
        ? orders
        : {
            sort: 'sortNumber',
            order: 'asc'
          };
    if (parent) {
      const data = await listMenus({
        ...where,
        ...orders,
        parentId: parent?.menuId || 0,
        menuType: routeType === 'btn' ? 2 : ''
      });
      // 为子级数据记录父级的 ElTable 懒加载的 resolve 方法
      if (parent) {
        data.forEach((d) => {
          d.hasChildren = d?.menuType === 2 ? false : true;
          if (parent && parent._tableResolve) {
            d._parentTableResolve = parent._tableResolve;
          }
        });
      }
      return data;
    } else {
      let obj = {
        ...where,
        page,
        limit,
        ...orders
      };
      obj.menuType = parent?.menuId || 0;
      obj.parentId =
        parent?.parentId || routeType === 'btn'
          ? '11111111111111111111111111111111'
          : '00000000000000000000000000000000';
      return pageMenus(obj);
    }
  };

  /** 操作更多*/
  const optItems = (row) => {
    let itemsArray = [];
    if (row.hasChildren) {
      itemsArray = [
        { title: '刷新子级', command: 'refreshChild' },
        { title: '刷新父级', command: 'refreshParent' }
      ];
    }
    return itemsArray;
  };

  /** 下拉菜单点击事件 */
  const dropClick = (key, row) => {
    if (key === 'refreshChild') {
      reloadChild(row);
    } else if (key === 'refreshParent') {
      reloadParent(row);
    }
  };

  /** 重写树表格懒加载方法 */
  const tableLoad = (row, treeNode, resolve) => {
    // 记录 ElTable 懒加载的 resolve 方法, 刷新时需要这个方法来更新子级的数据
    row._tableResolve = resolve;
    tableRef.value?.reload(void 0, row, resolve);
  };

  /** 刷新表格 */
  const reload = () => {
    // 按上面操作后每次调用表格的刷新就能保证子级数据也会更新了
    // tableRef.value?.reloadTable();
    if (currentClick.menuType === 1) {
      reloadParent(currentClick);
    } else if (currentClick.menuType === 2) {
      reloadChild(currentClick);
    } else {
      searchReload();
    }
  };
  /** 查询刷新*/
  const searchReload = (where) => {
    tableRef.value?.reload?.({ where });
  };

  // 例如当节点删除后可使用 reloadParent 刷新父级的整个子级
  // 例如父级有添加子级的按钮, 添加完后可以使用 reloadChild 刷新自己的子级
  /** 刷新节点的子级数据 */
  const reloadChild = (row) => {
    if (row._tableResolve) {
      tableRef.value?.reload(void 0, row, row._tableResolve);
    }
  };

  /** 刷新节点父级的子级数据 */
  const reloadParent = (row) => {
    if (row._parentTableResolve) {
      const parent = {
        _tableResolve: row._parentTableResolve,
        menuId: row.parentId
      };
      tableRef.value?.reload(void 0, parent, row._parentTableResolve);
    }
  };

  /** 打开编辑弹窗 */
  const openEdit = (row, type) => {
    if (type === 'add') {
      current.value = null; //添加data全部设置为空
      parentId.value = row
        ? row.menuId
        : routeType === 'btn'
          ? '11111111111111111111111111111111'
          : '00000000000000000000000000000000'; //行内添加菜单，把父级菜单ID带上
    } else {
      current.value = row;
      parentId.value = row.parentId;
    }
    currentClick.value = row ?? null;
    showEdit.value = true;
  };

  /** 删除单个 */
  const remove = (row) => {
    if (row.children?.length) {
      EleMessage.error('请先删除子节点');
      return;
    }
    ElMessageBox.confirm('确定要删除“' + row.title + '”吗?', '系统提示', {
      type: 'warning',
      draggable: true
    })
      .then(() => {
        const loading = EleMessage.loading('请求中..');
        removeMenu(row.menuId)
          .then((msg) => {
            loading.close();
            EleMessage.success(msg);
            if (row.menuType === 0) {
              searchReload();
            } else if (row.menuType === 1) {
              reloadParent(row);
            } else if (row.menuType === 2) {
              reloadChild(row);
            }
          })
          .catch((e) => {
            loading.close();
            EleMessage.error(e.message);
          });
      })
      .catch(() => {});
  };

  // /** 展开全部 */
  // const expandAll = () => {
  //     tableRef.value?.toggleRowExpansionAll?.(true);
  // };
  //
  // /** 折叠全部 */
  // const foldAll = () => {
  //     tableRef.value?.toggleRowExpansionAll?.(false);
  // };
</script>

<script>
  import * as MenuIcons from '@/layout/menu-icons';

  export default {
    name: 'SystemMenu',
    components: MenuIcons
  };
</script>
