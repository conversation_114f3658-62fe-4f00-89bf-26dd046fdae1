import { ref, reactive } from 'vue'

export function useDragDrop() {
  const dragState = reactive({
    isDragging: false,
    isDraggingZone: false,
    draggedField: null,
    draggedZone: null,
    startPos: { x: 0, y: 0 },
    offset: { x: 0, y: 0 }
  })

  const snapToGrid = ref(true)
  const gridSize = ref(5) // mm

  // 字段拖拽开始
  const handleFieldDragStart = (event, field) => {
    dragState.isDragging = true
    dragState.draggedField = field

    // 设置拖拽数据
    event.dataTransfer.setData('field', JSON.stringify(field))
    event.dataTransfer.effectAllowed = 'copy'

    // 创建拖拽预览
    const dragImage = createDragPreview(field)
    event.dataTransfer.setDragImage(dragImage, 10, 10)
  }

  // 字段拖拽结束
  const handleFieldDragEnd = () => {
    dragState.isDragging = false
    dragState.draggedField = null

    // 清理拖拽预览
    cleanupDragPreview()
  }

  // 区域拖拽开始
  const startZoneDrag = (event, zone) => {
    event.preventDefault()
    event.stopPropagation()

    dragState.isDraggingZone = true
    dragState.draggedZone = zone
    dragState.startPos = { x: event.clientX, y: event.clientY }

    // 计算鼠标相对于区域的偏移
    const canvas = document.querySelector('.label-canvas')
    if (canvas) {
      const canvasRect = canvas.getBoundingClientRect()
      const zoomLevel = parseFloat(canvas.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || 1)

      const zoneRect = event.currentTarget.getBoundingClientRect()
      dragState.offset = {
        x: (event.clientX - zoneRect.left) / zoomLevel,
        y: (event.clientY - zoneRect.top) / zoomLevel
      }
    }

    // 添加全局事件监听
    document.addEventListener('mousemove', handleZoneDragMove)
    document.addEventListener('mouseup', handleZoneDragEnd)
    document.body.style.cursor = 'move'
    document.body.style.userSelect = 'none'
  }

  // 区域拖拽移动
  const handleZoneDragMove = (event) => {
    if (!dragState.isDraggingZone || !dragState.draggedZone) return

    const canvas = document.querySelector('.label-canvas')
    if (!canvas) return

    const canvasRect = canvas.getBoundingClientRect()
    const zoomLevel = parseFloat(canvas.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || 1)

    // 计算新位置（转换为mm）
    const newX = ((event.clientX - canvasRect.left) / zoomLevel - dragState.offset.x) * 0.264583
    const newY = ((event.clientY - canvasRect.top) / zoomLevel - dragState.offset.y) * 0.264583

    // 获取画布和区域尺寸
    const canvasWidth = parseFloat(canvas.style.width)
    const canvasHeight = parseFloat(canvas.style.height)
    const zoneWidth = dragState.draggedZone.width
    const zoneHeight = dragState.draggedZone.height

    // 应用网格吸附
    let finalX = newX
    let finalY = newY

    if (snapToGrid.value) {
      finalX = Math.round(newX / gridSize.value) * gridSize.value
      finalY = Math.round(newY / gridSize.value) * gridSize.value
    }

    // 边界约束
    finalX = Math.max(0, Math.min(finalX, canvasWidth - zoneWidth))
    finalY = Math.max(0, Math.min(finalY, canvasHeight - zoneHeight))

    // 更新区域位置
    dragState.draggedZone.x = finalX
    dragState.draggedZone.y = finalY
  }

  // 区域拖拽结束
  const handleZoneDragEnd = () => {
    if (!dragState.isDraggingZone) return

    dragState.isDraggingZone = false

    // 清理事件监听和样式
    document.removeEventListener('mousemove', handleZoneDragMove)
    document.removeEventListener('mouseup', handleZoneDragEnd)
    document.body.style.cursor = ''
    document.body.style.userSelect = ''

    // 触发历史记录保存
    if (dragState.draggedZone && window.labelDesignerSaveState) {
      window.labelDesignerSaveState('移动区域')
    }

    dragState.draggedZone = null
  }

  // 创建拖拽预览
  const createDragPreview = (field) => {
    const preview = document.createElement('div')
    preview.className = 'drag-preview'
    preview.textContent = field.displayName || field.fieldName
    preview.style.cssText = `
      position: absolute;
      top: -1000px;
      left: -1000px;
      padding: 4px 8px;
      background: #409eff;
      color: white;
      border-radius: 4px;
      font-size: 12px;
      white-space: nowrap;
      z-index: 9999;
    `
    document.body.appendChild(preview)
    return preview
  }

  // 清理拖拽预览
  const cleanupDragPreview = () => {
    const preview = document.querySelector('.drag-preview')
    if (preview) {
      document.body.removeChild(preview)
    }
  }

  // 处理画布拖放
  const handleCanvasDrop = (event, findZoneAtPosition, createNewZone) => {
    event.preventDefault()

    try {
      const fieldData = JSON.parse(event.dataTransfer.getData('field'))

      // 计算放置位置
      const canvas = event.currentTarget
      const rect = canvas.getBoundingClientRect()
      const zoomLevel = parseFloat(canvas.style.transform?.match(/scale\(([\d.]+)\)/)?.[1] || 1)

      const x = ((event.clientX - rect.left) / zoomLevel) * 0.264583 // px to mm
      const y = ((event.clientY - rect.top) / zoomLevel) * 0.264583

      // 应用网格吸附
      let finalX = x
      let finalY = y

      if (snapToGrid.value) {
        finalX = Math.round(x / gridSize.value) * gridSize.value
        finalY = Math.round(y / gridSize.value) * gridSize.value
      }

      // 查找目标区域或创建新区域
      const targetZone = findZoneAtPosition(finalX, finalY)
      if (targetZone) {
        // 添加字段到现有区域
        if (!targetZone.fields) targetZone.fields = []
        if (!targetZone.fields.includes(fieldData.fieldName)) {
          targetZone.fields.push(fieldData.fieldName)
          if (window.labelDesignerSaveState) {
            window.labelDesignerSaveState('添加字段')
          }
        }
      } else {
        // 创建新区域
        createNewZone(fieldData, finalX, finalY)
      }
    } catch (error) {
      console.error('处理拖放失败:', error)
    }
  }

  // 处理拖拽悬停
  const handleDragOver = (event) => {
    event.preventDefault()
    event.dataTransfer.dropEffect = 'copy'
  }

  // 设置网格吸附
  const setSnapToGrid = (enabled) => {
    snapToGrid.value = enabled
  }

  // 设置网格大小
  const setGridSize = (size) => {
    gridSize.value = size
  }

  // 检查是否正在拖拽
  const isDraggingField = () => {
    return dragState.isDragging
  }

  const isDraggingZone = () => {
    return dragState.isDraggingZone
  }

  // 取消拖拽
  const cancelDrag = () => {
    if (dragState.isDraggingZone) {
      handleZoneDragEnd()
    }
    if (dragState.isDragging) {
      handleFieldDragEnd()
    }
  }

  // 键盘事件处理
  const handleKeydown = (event) => {
    if (dragState.isDraggingZone || dragState.isDragging) {
      if (event.key === 'Escape') {
        event.preventDefault()
        cancelDrag()
      }
    }
  }

  // 初始化
  const initDragDrop = (saveStateCallback) => {
    if (saveStateCallback) {
      window.labelDesignerSaveState = saveStateCallback
    }

    document.addEventListener('keydown', handleKeydown)
  }

  // 清理
  const destroyDragDrop = () => {
    cancelDrag()
    cleanupDragPreview()
    document.removeEventListener('keydown', handleKeydown)
    delete window.labelDesignerSaveState
  }

  return {
    dragState,
    snapToGrid,
    handleFieldDragStart,
    handleFieldDragEnd,
    startZoneDrag,
    handleCanvasDrop,
    handleDragOver,
    setSnapToGrid,
    setGridSize,
    isDraggingField,
    isDraggingZone,
    cancelDrag,
    initDragDrop,
    destroyDragDrop
  }
}
