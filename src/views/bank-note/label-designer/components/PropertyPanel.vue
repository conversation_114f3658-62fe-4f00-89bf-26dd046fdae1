<template>
  <div class="property-panel">
    <div class="panel-header">
      <span>属性面板</span>
    </div>

    <div class="panel-content" v-if="selectedZone">
      <!-- 基本信息 -->
      <div class="property-section">
        <h4 class="section-title">基本信息</h4>
        <el-form :model="selectedZone" label-width="80px" size="small">
          <el-form-item label="区域名称">
            <el-input
              v-model="selectedZone.name"
              @change="handlePropertyChange('name')"
              placeholder="请输入区域名称"
            />
          </el-form-item>
        </el-form>
      </div>

      <!-- 位置和尺寸 -->
      <div class="property-section">
        <h4 class="section-title">位置和尺寸</h4>
        <el-form :model="selectedZone" label-width="80px" size="small">
          <el-form-item label="位置">
            <div class="position-inputs">
              <el-input-number
                v-model="selectedZone.x"
                :min="0"
                :max="canvasWidth - selectedZone.width"
                :step="1"
                :precision="1"
                @change="handlePropertyChange('position')"
                style="width: 80px"
              />
              <span class="input-separator">×</span>
              <el-input-number
                v-model="selectedZone.y"
                :min="0"
                :max="canvasHeight - selectedZone.height"
                :step="1"
                :precision="1"
                @change="handlePropertyChange('position')"
                style="width: 80px"
              />
              <span class="unit">mm</span>
            </div>
          </el-form-item>

          <el-form-item label="尺寸">
            <div class="size-inputs">
              <el-input-number
                v-model="selectedZone.width"
                :min="10"
                :max="canvasWidth - selectedZone.x"
                :step="1"
                :precision="1"
                @change="handlePropertyChange('size')"
                style="width: 80px"
              />
              <span class="input-separator">×</span>
              <el-input-number
                v-model="selectedZone.height"
                :min="10"
                :max="canvasHeight - selectedZone.y"
                :step="1"
                :precision="1"
                @change="handlePropertyChange('size')"
                style="width: 80px"
              />
              <span class="unit">mm</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 样式设置 -->
      <div class="property-section">
        <h4 class="section-title">样式设置</h4>
        <el-form :model="selectedZone" label-width="80px" size="small">
          <el-form-item label="字体大小">
            <el-input-number
              v-model="selectedZone.fontSize"
              :min="8"
              :max="72"
              :step="1"
              @change="handlePropertyChange('fontSize')"
              style="width: 100px"
            />
            <span class="unit">px</span>
          </el-form-item>

          <el-form-item label="文字颜色">
            <el-color-picker
              v-model="selectedZone.color"
              @change="handlePropertyChange('color')"
              show-alpha
            />
          </el-form-item>

          <el-form-item label="背景颜色">
            <el-color-picker
              v-model="selectedZone.backgroundColor"
              @change="handlePropertyChange('backgroundColor')"
              show-alpha
            />
          </el-form-item>

          <!-- 间距设置 -->
          <el-form-item label="内边距">
            <div style="display: flex; gap: 8px; align-items: center;">
              <el-input-number
                v-model="selectedZone.padding"
                :min="0"
                :max="20"
                :step="1"
                @change="handlePropertyChange('padding')"
                style="width: 80px"
              />
              <span class="unit">px</span>
            </div>
          </el-form-item>

          <el-form-item label="字段间距">
            <div style="display: flex; gap: 8px; align-items: center;">
              <el-input-number
                v-model="selectedZone.fieldSpacing"
                :min="0"
                :max="20"
                :step="1"
                @change="handlePropertyChange('fieldSpacing')"
                style="width: 80px"
              />
              <span class="unit">px</span>
            </div>
          </el-form-item>

          <el-form-item label="行间距">
            <div style="display: flex; gap: 8px; align-items: center;">
              <el-input-number
                v-model="selectedZone.lineHeight"
                :min="1"
                :max="3"
                :step="0.1"
                @change="handlePropertyChange('lineHeight')"
                style="width: 80px"
              />
              <span class="unit">倍</span>
            </div>
          </el-form-item>
        </el-form>
      </div>

      <!-- 字段管理 -->
      <div class="property-section">
        <h4 class="section-title">字段管理</h4>
        <div class="field-management">
          <div class="current-fields" v-if="selectedZone.fields && selectedZone.fields.length">
            <div class="field-list-title">当前字段：</div>
            <div class="field-tags">
              <div
                v-for="fieldName in selectedZone.fields"
                :key="fieldName"
                class="field-item"
                @click="selectFieldForConfig(fieldName)"
                :class="{ 'field-item-selected': selectedFieldForConfig === fieldName }"
              >
                <el-tag
                  closable
                  @close="removeField(fieldName)"
                  size="small"
                  style="margin-right: 8px;"
                >
                  {{ getFieldDisplayName(fieldName) }}
                </el-tag>
                <el-button
                  type="text"
                  size="small"
                  @click.stop="selectFieldForConfig(fieldName)"
                  :class="{ 'config-btn-active': selectedFieldForConfig === fieldName }"
                >
                  配置
                </el-button>
              </div>
            </div>
          </div>

          <div class="add-field" v-if="availableFields.length">
            <el-select
              v-model="selectedFieldToAdd"
              placeholder="选择要添加的字段"
              size="small"
              style="width: 100%; margin-top: 8px"
              @change="addField"
            >
              <el-option
                v-for="field in availableFields"
                :key="field.fieldName"
                :label="field.displayName"
                :value="field.fieldName"
              />
            </el-select>
          </div>

          <div v-if="!selectedZone.fields || !selectedZone.fields.length" class="empty-fields">
            <el-text type="info" size="small">暂无字段，请从左侧拖拽字段到此区域</el-text>
          </div>
        </div>
      </div>

      <!-- 字段样式配置 -->
      <div class="property-section" v-if="selectedFieldForConfig">
        <h4 class="section-title">字段样式配置</h4>
        <div class="field-style-config">
          <el-form label-width="80px" size="small">
            <el-form-item>
              <template #label>
                <span>配置字段</span>
              </template>
              <el-tag size="small">{{ getFieldDisplayName(selectedFieldForConfig) }}</el-tag>
            </el-form-item>

            <el-form-item label="显示模式">
              <el-select
                :model-value="getFieldDisplayMode(selectedFieldForConfig)"
                @change="handleDisplayModeChange"
                style="width: 100%"
              >
                <el-option label="普通显示" value="normal" />
                <el-option label="大字体显示" value="large" />
                <el-option label="竖向字符" value="vertical" />
                <el-option label="分层显示" value="layered" />
                <el-optgroup label="评级信息专用">
                  <el-option label="评级分层显示" value="grade-layered" />
                  <el-option label="仅显示分数" value="grade-score-only" />
                  <el-option label="仅显示等级" value="grade-level-only" />
                  <el-option label="仅显示标记" value="grade-mark-only" />
                </el-optgroup>
              </el-select>
            </el-form-item>

            <!-- 评级分层显示的特殊配置 -->
            <template v-if="getFieldDisplayMode(selectedFieldForConfig) === 'grade-layered'">
              <el-form-item label="数字字体大小">
                <el-input-number
                  :model-value="getFieldScoreFontSize(selectedFieldForConfig)"
                  @change="handleScoreFontSizeChange"
                  :min="16"
                  :max="48"
                  :step="1"
                  style="width: 100%"
                />
                <div class="field-hint">评级分数的字体大小（建议24-36px）</div>
              </el-form-item>

              <el-form-item label="文字字体大小">
                <el-input-number
                  :model-value="getFieldLevelFontSize(selectedFieldForConfig)"
                  @change="handleLevelFontSizeChange"
                  :min="8"
                  :max="24"
                  :step="1"
                  style="width: 100%"
                />
                <div class="field-hint">评级等级的字体大小（建议10-14px）</div>
              </el-form-item>
            </template>

            <!-- 评级分数分离字段的特殊配置 -->
            <template v-else-if="isGradeScoreField(selectedFieldForConfig)">
              <el-form-item label="数字字体大小" v-if="selectedFieldForConfig === 'gradeScoreNumber'">
                <el-input-number
                  :model-value="getFieldFontSize(selectedFieldForConfig)"
                  @change="handleFontSizeChange"
                  :min="12"
                  :max="48"
                  :step="1"
                  style="width: 100%"
                />
                <div class="field-hint">建议使用较大字体（24-36px）</div>
              </el-form-item>

              <el-form-item label="文字字体大小" v-if="selectedFieldForConfig === 'gradeScoreText'">
                <el-input-number
                  :model-value="getFieldFontSize(selectedFieldForConfig)"
                  @change="handleFontSizeChange"
                  :min="8"
                  :max="24"
                  :step="1"
                  style="width: 100%"
                />
                <div class="field-hint">建议使用较小字体（10-14px）</div>
              </el-form-item>
            </template>

            <!-- 普通字段的字体大小配置 -->
            <el-form-item label="字体大小" v-else-if="getFieldDisplayMode(selectedFieldForConfig) !== 'normal'">
              <el-input-number
                :model-value="getFieldFontSize(selectedFieldForConfig)"
                @change="handleFontSizeChange"
                :min="8"
                :max="48"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="字符间距" v-if="getFieldDisplayMode(selectedFieldForConfig) === 'vertical'">
              <el-input-number
                :model-value="getFieldLetterSpacing(selectedFieldForConfig)"
                @change="handleLetterSpacingChange"
                :min="0"
                :max="20"
                :step="1"
                style="width: 100%"
              />
            </el-form-item>

            <el-form-item label="行高" v-if="getFieldDisplayMode(selectedFieldForConfig) === 'layered'">
              <el-input-number
                :model-value="getFieldLineHeight(selectedFieldForConfig)"
                @change="handleLineHeightChange"
                :min="1"
                :max="3"
                :step="0.1"
                style="width: 100%"
              />
            </el-form-item>

            <!-- 二维码专用配置 -->
            <template v-if="isQRCodeField(selectedFieldForConfig)">
              <el-form-item label="二维码大小">
                <el-input-number
                  :model-value="getQRCodeSize(selectedFieldForConfig)"
                  @change="handleQRCodeSizeChange"
                  :min="30"
                  :max="150"
                  :step="5"
                  style="width: 100%"
                />
              </el-form-item>

              <el-form-item label="显示文本">
                <el-switch
                  :model-value="getQRCodeShowText(selectedFieldForConfig)"
                  @change="handleQRCodeShowTextChange"
                />
              </el-form-item>

              <el-form-item label="文本大小" v-if="getQRCodeShowText(selectedFieldForConfig)">
                <el-input-number
                  :model-value="getQRCodeTextSize(selectedFieldForConfig)"
                  @change="handleQRCodeTextSizeChange"
                  :min="8"
                  :max="16"
                  :step="1"
                  style="width: 100%"
                />
              </el-form-item>
            </template>

            <el-form-item>
              <el-button
                type="primary"
                size="small"
                @click="applyFieldStyle"
                style="width: 100%"
              >
                应用样式
              </el-button>
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 字段组合配置 -->
      <div class="property-section">
        <h4 class="section-title">字段组合配置</h4>
        <div class="field-combination">
          <el-form label-width="80px" size="small">
            <el-form-item label="启用组合">
              <el-switch
                v-model="fieldCombination.enabled"
                @change="handleCombinationChange"
              />
            </el-form-item>

            <template v-if="fieldCombination.enabled">
              <el-form-item label="第一字段">
                <el-select
                  v-model="fieldCombination.firstField"
                  placeholder="选择第一个字段"
                  style="width: 100%"
                  @change="handleCombinationChange"
                >
                  <el-option
                    v-for="field in allFields"
                    :key="field.fieldName"
                    :label="field.displayName"
                    :value="field.fieldName"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="分隔符">
                <el-input
                  v-model="fieldCombination.separator"
                  placeholder="输入分隔符"
                  style="width: 100%"
                  @input="handleCombinationChange"
                />
              </el-form-item>

              <el-form-item label="第二字段">
                <el-select
                  v-model="fieldCombination.secondField"
                  placeholder="选择第二个字段"
                  style="width: 100%"
                  @change="handleCombinationChange"
                >
                  <el-option
                    v-for="field in allFields"
                    :key="field.fieldName"
                    :label="field.displayName"
                    :value="field.fieldName"
                  />
                </el-select>
              </el-form-item>

              <el-form-item label="显示方式">
                <el-radio-group
                  v-model="fieldCombination.layout"
                  @change="handleCombinationChange"
                >
                  <el-radio value="inline">同一行</el-radio>
                  <el-radio value="vertical">分行显示</el-radio>
                </el-radio-group>
              </el-form-item>

              <el-form-item>
                <el-button
                  type="primary"
                  size="small"
                  @click="addCombinationField"
                  :disabled="!canAddCombination"
                  style="width: 100%"
                >
                  添加组合字段
                </el-button>
              </el-form-item>

              <el-form-item>
                <div class="combination-preview">
                  <el-text type="info" size="small">预览：</el-text>
                  <div class="preview-content">
                    {{ getCombinationPreview() }}
                  </div>
                </div>
              </el-form-item>
            </template>
          </el-form>
        </div>
      </div>

      <!-- 层级管理 -->
      <div class="property-section">
        <h4 class="section-title">层级管理</h4>
        <div class="layer-controls">
          <el-button-group size="small">
            <el-button @click="moveLayer('top')" :icon="Top">置顶</el-button>
            <el-button @click="moveLayer('up')" :icon="ArrowUp">上移</el-button>
            <el-button @click="moveLayer('down')" :icon="ArrowDown">下移</el-button>
            <el-button @click="moveLayer('bottom')" :icon="Bottom">置底</el-button>
          </el-button-group>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="property-section">
        <h4 class="section-title">操作</h4>
        <div class="action-buttons">
          <el-button size="small" @click="copyZone" :icon="DocumentCopy">
            复制区域
          </el-button>
          <el-button size="small" type="danger" @click="deleteZone" :icon="Delete">
            删除区域
          </el-button>
        </div>
      </div>
    </div>

    <!-- 未选中状态 - 显示画布设置 -->
    <div class="panel-content" v-else>
      <!-- 画布设置 -->
      <div class="property-section">
        <h4 class="section-title">画布设置</h4>
        <el-form label-width="80px" size="small">
          <el-form-item label="模板类型">
            <el-select
              v-model="currentTemplateType"
              @change="handleTemplateTypeChange"
              placeholder="选择模板类型"
              style="width: 100%"
            >
              <el-option label="大签标签 (192×26mm)" value="large" />
              <el-option label="小签标签 (115×21mm)" value="small" />
              <el-option label="自定义尺寸" value="custom" />
            </el-select>
          </el-form-item>

          <el-form-item label="画布尺寸">
            <div class="canvas-size-inputs">
              <el-input-number
                :model-value="canvasWidth"
                @change="handleCanvasSizeChange('width', $event)"
                :min="50"
                :max="500"
                :step="1"
                :precision="0"
                style="width: 90px"
                :disabled="currentTemplateType !== 'custom'"
                placeholder="宽度"
              />
              <span class="input-separator">×</span>
              <el-input-number
                :model-value="canvasHeight"
                @change="handleCanvasSizeChange('height', $event)"
                :min="10"
                :max="100"
                :step="1"
                :precision="0"
                style="width: 90px"
                :disabled="currentTemplateType !== 'custom'"
                placeholder="高度"
              />
              <span class="unit">mm</span>
            </div>
            <div v-if="currentTemplateType !== 'custom'" class="size-tip">
              <el-text type="info" size="small">选择"自定义尺寸"来手动调节大小</el-text>
            </div>
          </el-form-item>

          <el-form-item>
            <div class="template-info">
              <el-alert
                :title="`当前尺寸: ${canvasWidth}mm × ${canvasHeight}mm`"
                type="info"
                :closable="false"
                show-icon
              />
            </div>
          </el-form-item>
        </el-form>
      </div>

      <div class="empty-state">
        <el-icon size="48" color="#c0c4cc">
          <Select />
        </el-icon>
        <p>请选择一个区域来编辑属性</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, reactive } from 'vue'
import {
  DocumentCopy,
  Delete,
  Select,
  Top,
  Bottom,
  ArrowUp,
  ArrowDown
} from '@element-plus/icons-vue'

const props = defineProps({
  selectedZone: {
    type: Object,
    default: null
  },
  labelZones: {
    type: Array,
    default: () => []
  },
  fieldCategories: {
    type: Object,
    default: () => ({})
  },
  canvasWidth: {
    type: Number,
    default: 200
  },
  canvasHeight: {
    type: Number,
    default: 25
  }
})

const emit = defineEmits([
  'property-change',
  'copy-zone',
  'delete-zone',
  'move-layer',
  'canvas-size-change',
  'template-type-change'
])

const selectedFieldToAdd = ref('')
const currentTemplateType = ref('large') // 当前选中的模板类型
const selectedFieldForConfig = ref('') // 当前选中要配置的字段

// 字段组合配置
const fieldCombination = reactive({
  enabled: false,
  firstField: '',
  separator: ' - ',
  secondField: '',
  layout: 'inline' // inline: 同一行, vertical: 分行显示
})

// 字段样式配置
const fieldStyleConfig = reactive({
  displayMode: 'normal', // normal, large, vertical, layered
  fontSize: 12,
  letterSpacing: 2,
  lineHeight: 1.2,
  // 评级分层显示专用配置
  scoreFontSize: 28,
  levelFontSize: 12,
  // 二维码专用配置
  qrSize: 60,
  showText: true,
  textSize: 10
})

// 获取所有可用字段
const allFields = computed(() => {
  const fields = []
  Object.values(props.fieldCategories).forEach(categoryFields => {
    fields.push(...categoryFields)
  })
  return fields
})

// 获取可添加的字段（排除已添加的）
const availableFields = computed(() => {
  if (!props.selectedZone || !props.selectedZone.fields) {
    return allFields.value
  }
  return allFields.value.filter(field =>
    !props.selectedZone.fields.includes(field.fieldName)
  )
})

// 获取字段显示名称
const getFieldDisplayName = (fieldName) => {
  const field = allFields.value.find(f => f.fieldName === fieldName)
  return field ? field.displayName : fieldName
}

// 检查是否可以添加组合字段
const canAddCombination = computed(() => {
  return fieldCombination.enabled &&
         fieldCombination.firstField &&
         fieldCombination.secondField &&
         fieldCombination.separator
})

// 获取组合字段预览
const getCombinationPreview = () => {
  if (!fieldCombination.firstField || !fieldCombination.secondField) {
    return '请选择字段'
  }

  const firstFieldName = getFieldDisplayName(fieldCombination.firstField)
  const secondFieldName = getFieldDisplayName(fieldCombination.secondField)

  if (fieldCombination.layout === 'vertical') {
    return `${firstFieldName}\n${secondFieldName}`
  } else {
    return `${firstFieldName}${fieldCombination.separator}${secondFieldName}`
  }
}

// 属性变更处理
const handlePropertyChange = (type) => {
  // 确保间距属性有默认值
  if (props.selectedZone) {
    if (props.selectedZone.padding === undefined) {
      props.selectedZone.padding = 2
    }
    if (props.selectedZone.fieldSpacing === undefined) {
      props.selectedZone.fieldSpacing = 2
    }
    if (props.selectedZone.lineHeight === undefined) {
      props.selectedZone.lineHeight = 1.2
    }
  }

  emit('property-change', {
    zone: props.selectedZone,
    type
  })
}

// 添加字段
const addField = (fieldName) => {
  if (!fieldName || !props.selectedZone) return

  if (!props.selectedZone.fields) {
    props.selectedZone.fields = []
  }

  if (!props.selectedZone.fields.includes(fieldName)) {
    props.selectedZone.fields.push(fieldName)
    handlePropertyChange('添加字段')
  }

  selectedFieldToAdd.value = ''
}

// 移除字段
const removeField = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fields) return

  const index = props.selectedZone.fields.indexOf(fieldName)
  if (index > -1) {
    props.selectedZone.fields.splice(index, 1)
    handlePropertyChange('移除字段')
  }
}

// 复制区域
const copyZone = () => {
  emit('copy-zone', props.selectedZone)
}

// 删除区域
const deleteZone = () => {
  emit('delete-zone', props.selectedZone)
}

// 移动层级
const moveLayer = (action) => {
  emit('move-layer', {
    zone: props.selectedZone,
    action
  })
}

// 预设模板尺寸
const templateSizes = {
  large: { width: 192, height: 26 },  // 大签
  small: { width: 115, height: 21 },  // 小签
  custom: { width: props.canvasWidth, height: props.canvasHeight }
}

// 处理模板类型变更
const handleTemplateTypeChange = (type) => {
  currentTemplateType.value = type

  if (type !== 'custom') {
    const size = templateSizes[type]
    emit('canvas-size-change', { width: size.width, height: size.height })
  }
  emit('template-type-change', type)
}

// 处理画布尺寸变更
const handleCanvasSizeChange = (dimension, value) => {
  const newSize = {
    width: dimension === 'width' ? value : props.canvasWidth,
    height: dimension === 'height' ? value : props.canvasHeight
  }

  // 当用户手动调整尺寸时，自动切换到自定义模式
  if (currentTemplateType.value !== 'custom') {
    currentTemplateType.value = 'custom'
  }

  emit('canvas-size-change', newSize)
}

// 监听画布尺寸变化，自动检测模板类型
watch([() => props.canvasWidth, () => props.canvasHeight], ([newWidth, newHeight]) => {
  // 检查是否匹配预设尺寸
  if (newWidth === templateSizes.large.width && newHeight === templateSizes.large.height) {
    if (currentTemplateType.value !== 'large') {
      currentTemplateType.value = 'large'
    }
  } else if (newWidth === templateSizes.small.width && newHeight === templateSizes.small.height) {
    if (currentTemplateType.value !== 'small') {
      currentTemplateType.value = 'small'
    }
  } else {
    if (currentTemplateType.value !== 'custom') {
      currentTemplateType.value = 'custom'
    }
  }
})

// 处理组合字段配置变更
const handleCombinationChange = () => {
  // 可以在这里添加实时预览或验证逻辑
  console.log('组合配置变更:', fieldCombination)
}

// 添加组合字段
const addCombinationField = () => {
  if (!canAddCombination.value || !props.selectedZone) return

  // 生成组合字段名称
  const combinationFieldName = `${fieldCombination.firstField}_${fieldCombination.separator.replace(/\s/g, '')}_${fieldCombination.secondField}_${fieldCombination.layout}`

  // 创建组合字段配置
  const combinationConfig = {
    type: 'combination',
    firstField: fieldCombination.firstField,
    separator: fieldCombination.separator,
    secondField: fieldCombination.secondField,
    layout: fieldCombination.layout,
    displayName: getCombinationPreview()
  }

  // 添加到区域的组合字段配置中
  if (!props.selectedZone.combinationFields) {
    props.selectedZone.combinationFields = {}
  }
  props.selectedZone.combinationFields[combinationFieldName] = combinationConfig

  // 添加到字段列表中
  if (!props.selectedZone.fields) {
    props.selectedZone.fields = []
  }

  if (!props.selectedZone.fields.includes(combinationFieldName)) {
    props.selectedZone.fields.push(combinationFieldName)
    handlePropertyChange('fields')
  }

  // 重置组合配置
  fieldCombination.firstField = ''
  fieldCombination.secondField = ''
  fieldCombination.separator = ' - '
  fieldCombination.layout = 'inline'
  fieldCombination.enabled = false
}

// 选择字段进行配置
const selectFieldForConfig = (fieldName) => {
  selectedFieldForConfig.value = fieldName
  // 加载该字段的当前配置
  loadFieldStyleConfig(fieldName)
}

// 加载字段样式配置
const loadFieldStyleConfig = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles) {
    // 使用默认配置
    fieldStyleConfig.displayMode = 'normal'
    fieldStyleConfig.fontSize = 12
    fieldStyleConfig.letterSpacing = 2
    fieldStyleConfig.lineHeight = 1.2
    fieldStyleConfig.scoreFontSize = 28
    fieldStyleConfig.levelFontSize = 12
    fieldStyleConfig.qrSize = 60
    fieldStyleConfig.showText = true
    fieldStyleConfig.textSize = 10
    return
  }

  const fieldStyle = props.selectedZone.fieldStyles[fieldName]
  if (fieldStyle) {
    fieldStyleConfig.displayMode = fieldStyle.displayMode || 'normal'
    fieldStyleConfig.fontSize = fieldStyle.fontSize || 12
    fieldStyleConfig.letterSpacing = fieldStyle.letterSpacing || 2
    fieldStyleConfig.lineHeight = fieldStyle.lineHeight || 1.2
    fieldStyleConfig.qrSize = fieldStyle.qrSize || 60
    fieldStyleConfig.showText = fieldStyle.showText !== false
    fieldStyleConfig.textSize = fieldStyle.textSize || 10
    // 加载评级分层显示的专用配置
    fieldStyleConfig.scoreFontSize = fieldStyle.scoreFontSize || 28
    fieldStyleConfig.levelFontSize = fieldStyle.levelFontSize || 12
  }
}

// 获取字段显示模式
const getFieldDisplayMode = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 'normal'
  }
  return props.selectedZone.fieldStyles[fieldName].displayMode || 'normal'
}

// 获取字段字体大小
const getFieldFontSize = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    // 为分离的评级字段设置不同的默认字体大小
    if (fieldName === 'gradeScoreNumber') {
      return 28 // 数字部分默认较大
    } else if (fieldName === 'gradeScoreText') {
      return 12 // 文字部分默认较小
    }
    return 12
  }
  return props.selectedZone.fieldStyles[fieldName].fontSize || 12
}

// 获取评级分数字体大小（用于grade-layered模式）
const getFieldScoreFontSize = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 28 // 默认数字字体大小
  }
  return props.selectedZone.fieldStyles[fieldName].scoreFontSize || 28
}

// 获取评级等级字体大小（用于grade-layered模式）
const getFieldLevelFontSize = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 12 // 默认文字字体大小
  }
  return props.selectedZone.fieldStyles[fieldName].levelFontSize || 12
}

// 获取字段字符间距
const getFieldLetterSpacing = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 2
  }
  return props.selectedZone.fieldStyles[fieldName].letterSpacing || 2
}

// 获取字段行高
const getFieldLineHeight = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 1.2
  }
  return props.selectedZone.fieldStyles[fieldName].lineHeight || 1.2
}

// 处理显示模式变更
const handleDisplayModeChange = (value) => {
  fieldStyleConfig.displayMode = value
}

// 处理字体大小变更
const handleFontSizeChange = (value) => {
  fieldStyleConfig.fontSize = value
}

// 处理评级分数字体大小变更（用于grade-layered模式）
const handleScoreFontSizeChange = (value) => {
  fieldStyleConfig.scoreFontSize = value
  applyFieldStyle() // 自动应用样式
}

// 处理评级等级字体大小变更（用于grade-layered模式）
const handleLevelFontSizeChange = (value) => {
  fieldStyleConfig.levelFontSize = value
  applyFieldStyle() // 自动应用样式
}

// 处理字符间距变更
const handleLetterSpacingChange = (value) => {
  fieldStyleConfig.letterSpacing = value
}

// 处理行高变更
const handleLineHeightChange = (value) => {
  fieldStyleConfig.lineHeight = value
}

// 判断是否为二维码字段
const isQRCodeField = (fieldName) => {
  return fieldName === 'qrCode' || fieldName === 'qrCodeContent'
}

// 判断是否为评级分数分离字段
const isGradeScoreField = (fieldName) => {
  return fieldName === 'gradeScoreNumber' || fieldName === 'gradeScoreText'
}

// 获取二维码大小
const getQRCodeSize = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 60
  }
  return props.selectedZone.fieldStyles[fieldName].qrSize || 60
}

// 获取二维码是否显示文本
const getQRCodeShowText = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return true
  }
  return props.selectedZone.fieldStyles[fieldName].showText !== false
}

// 获取二维码文本大小
const getQRCodeTextSize = (fieldName) => {
  if (!props.selectedZone || !props.selectedZone.fieldStyles || !props.selectedZone.fieldStyles[fieldName]) {
    return 10
  }
  return props.selectedZone.fieldStyles[fieldName].textSize || 10
}

// 处理二维码大小变更
const handleQRCodeSizeChange = (value) => {
  fieldStyleConfig.qrSize = value
}

// 处理二维码显示文本变更
const handleQRCodeShowTextChange = (value) => {
  fieldStyleConfig.showText = value
}

// 处理二维码文本大小变更
const handleQRCodeTextSizeChange = (value) => {
  fieldStyleConfig.textSize = value
}

// 应用字段样式
const applyFieldStyle = () => {
  if (!selectedFieldForConfig.value || !props.selectedZone) return

  // 初始化字段样式配置
  if (!props.selectedZone.fieldStyles) {
    props.selectedZone.fieldStyles = {}
  }

  // 保存字段样式配置
  const styleConfig = {
    displayMode: fieldStyleConfig.displayMode,
    fontSize: fieldStyleConfig.fontSize,
    letterSpacing: fieldStyleConfig.letterSpacing,
    lineHeight: fieldStyleConfig.lineHeight
  }

  // 如果是评级分层显示，添加专用配置
  if (fieldStyleConfig.displayMode === 'grade-layered') {
    styleConfig.scoreFontSize = fieldStyleConfig.scoreFontSize
    styleConfig.levelFontSize = fieldStyleConfig.levelFontSize
    console.log('保存评级分层配置:', {
      scoreFontSize: fieldStyleConfig.scoreFontSize,
      levelFontSize: fieldStyleConfig.levelFontSize,
      fieldName: selectedFieldForConfig.value
    })
  }

  // 如果是二维码字段，添加二维码专用配置
  if (isQRCodeField(selectedFieldForConfig.value)) {
    styleConfig.qrSize = fieldStyleConfig.qrSize
    styleConfig.showText = fieldStyleConfig.showText
    styleConfig.textSize = fieldStyleConfig.textSize
  }

  props.selectedZone.fieldStyles[selectedFieldForConfig.value] = styleConfig

  // 触发属性变更
  handlePropertyChange('fieldStyles')

  // 提示用户
  console.log(`字段 ${selectedFieldForConfig.value} 样式已应用`)
}

// 监听选中区域变化，重置字段选择
watch(() => props.selectedZone, () => {
  selectedFieldToAdd.value = ''
  selectedFieldForConfig.value = ''
  // 重置组合字段配置
  fieldCombination.enabled = false
  fieldCombination.firstField = ''
  fieldCombination.secondField = ''
  fieldCombination.separator = ' - '
  fieldCombination.layout = 'inline'
})
</script>

<style scoped>
.property-panel {
  width: 380px; /* 增加宽度从320px到380px */
  background: white;
  border-left: 1px solid #e4e7ed;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 60px); /* 设置高度为视窗高度减去工具栏高度 */
  min-height: 600px; /* 设置最小高度 */
}

.panel-header {
  height: 50px;
  padding: 0 16px;
  border-bottom: 1px solid #e4e7ed;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-weight: 600;
}

.panel-content {
  flex: 1;
  overflow-y: auto;
  padding: 16px;
  max-height: calc(100vh - 110px); /* 确保内容区域不会超出视窗 */
  min-height: 500px; /* 设置最小高度确保有足够的操作空间 */
}

.property-section {
  margin-bottom: 28px; /* 增加各部分之间的间距 */
  padding: 16px; /* 添加内边距 */
  background: #fafbfc; /* 添加浅色背景区分各部分 */
  border-radius: 6px; /* 添加圆角 */
  border: 1px solid #f0f2f5; /* 添加边框 */
}

.section-title {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #f0f0f0;
  padding-bottom: 8px;
}

.position-inputs,
.size-inputs,
.canvas-size-inputs {
  display: flex;
  align-items: center;
  gap: 8px;
}

.input-separator {
  color: #909399;
  font-weight: bold;
}

.unit {
  color: #909399;
  font-size: 12px;
}

.template-info {
  margin-top: 8px;
}

.size-tip {
  margin-top: 4px;
  text-align: center;
}

.field-combination {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.combination-preview {
  margin-top: 8px;
  padding: 8px;
  background: #fff;
  border: 1px solid #e4e7ed;
  border-radius: 4px;
}

.preview-content {
  margin-top: 4px;
  font-family: monospace;
  font-size: 12px;
  color: #606266;
  white-space: pre-line;
  min-height: 20px;
}

.field-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 4px 8px;
  margin: 2px 0;
  border-radius: 4px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.field-item:hover {
  background-color: #f5f7fa;
}

.field-item-selected {
  background-color: #e6f7ff;
  border: 1px solid #1890ff;
}

.config-btn-active {
  color: #1890ff;
  font-weight: bold;
}

.field-style-config {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.field-management {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 12px;
}

.field-list-title {
  font-size: 12px;
  color: #606266;
  margin-bottom: 8px;
}

.field-tags {
  margin-bottom: 8px;
}

.empty-fields {
  text-align: center;
  padding: 20px 0;
}

.layer-controls {
  display: flex;
  justify-content: center;
}

.action-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 20px;
  color: #909399;
}

.empty-state p {
  margin: 16px 0 0 0;
  font-size: 14px;
}

:deep(.el-form-item) {
  margin-bottom: 20px; /* 增加表单项之间的间距 */
}

:deep(.el-form-item__label) {
  font-size: 13px; /* 稍微增大标签字体 */
  color: #606266;
  line-height: 1.5; /* 增加行高 */
  padding-bottom: 8px; /* 增加标签下方间距 */
}

.unit {
  color: #909399;
  font-size: 12px;
  margin-left: 4px;
}

.field-hint {
  font-size: 11px;
  color: #909399;
  margin-top: 4px;
  line-height: 1.2;
}



:deep(.el-input-number) {
  width: 100%;
}

:deep(.el-color-picker) {
  width: 100%;
}

/* 响应式设计 */
@media (max-height: 800px) {
  .property-panel {
    height: calc(100vh - 60px);
    min-height: 400px;
  }

  .panel-content {
    max-height: calc(100vh - 110px);
    min-height: 350px;
  }
}

@media (max-height: 600px) {
  .property-panel {
    height: calc(100vh - 60px);
    min-height: 300px;
  }

  .panel-content {
    max-height: calc(100vh - 110px);
    min-height: 250px;
  }
}

/* 大屏幕优化 */
@media (min-height: 1200px) {
  .property-panel {
    min-height: 800px;
  }

  .panel-content {
    min-height: 700px;
  }
}
</style>
