import request from '@/utils/request';

/**
 * 获取可用字段列表
 */
export async function getAvailableFields() {
  const res = await request.get('/label-design/fields');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 按分类获取字段列表
 */
export async function getFieldsByCategory() {
  const res = await request.get('/label-design/fields/by-category');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 保存标签模板
 */
export async function saveTemplate(templateData) {
  const res = await request.post('/label-design/template', templateData);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取模板列表
 */
export async function getTemplateList() {
  const res = await request.get('/label-design/templates');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取默认模板
 */
export async function getDefaultTemplate() {
  const res = await request.get('/label-design/template/default');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 预览标签效果
 */
export async function previewLabel(templateId, coinIds) {
  const res = await request.post(`/label-design/preview?templateId=${templateId}`, coinIds);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 删除标签模板
 */
export async function deleteTemplate(id) {
  const res = await request.delete(`/label-design/template/${id}`);
  if (res.data.code === 0) {
    return res.data.message;
  }
  return Promise.reject(new Error(res.data.message));
}
