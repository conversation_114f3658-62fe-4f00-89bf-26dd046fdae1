<template>
  <div class="batch-print-page">
    <!-- 查询条件 -->
    <SearchComponent
      ref="searchRef"
      :loading="loading"
      @search="handleSearch"
      @reset="handleReset"
    />

    <!-- 打印配置 -->
    <PrintConfig
      ref="printConfigRef"
      :selected-coins="selectedCoins"
      :all-coins="coinList"
      @print-label="handlePrintLabel"
      @print-white="handlePrintWhite"
      @audit-check="handleAuditCheck"
    />

    <!-- 数据表格 -->
    <el-card shadow="never">
      <template #header>
        <div class="table-header">
          <span>钱币列表</span>
          <div class="header-actions">
            <el-button
              :icon="Refresh"
              @click="handleRefresh"
              :loading="loading"
            >
              刷新
            </el-button>
          </div>
        </div>
      </template>

      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="coinList"
        border
        stripe
        height="500"
        @selection-change="handleSelectionChange"
      >
        <!-- 选择列 -->
        <el-table-column type="selection" width="50" />

        <!-- 钱币编号 -->
        <el-table-column
          prop="serialNumber"
          label="钱币编号"
          width="120"
          show-overflow-tooltip
        />

        <!-- 送评单号 -->
        <el-table-column
          prop="sendformNumber"
          label="送评单号"
          width="120"
          show-overflow-tooltip
        />

        <!-- 鉴定结果 -->
        <el-table-column
          prop="authenticity"
          label="结果"
          width="100"
          align="center"
        >
        </el-table-column>

        <!-- 钱币名称 -->
        <el-table-column
          prop="coinName"
          label="名称"
          width="150"
          show-overflow-tooltip
        />

        <!-- 版别 -->
        <el-table-column
          prop="version"
          label="版别"
          width="120"
          show-overflow-tooltip
        />

        <!-- 附加信息 -->
        <el-table-column
          prop="additionalInfo"
          label="附加"
          width="120"
          show-overflow-tooltip
        />

        <!-- 年代 -->
        <el-table-column
          prop="yearInfo"
          label="年代"
          width="100"
          show-overflow-tooltip
        />

        <!-- 等级 -->
        <el-table-column
          prop="gradeLevel"
          label="等级"
          width="80"
          align="center"
        />

        <!-- 重量 -->
        <el-table-column
          prop="weight"
          label="重量"
          width="80"
          align="center"
        />

        <!-- 尺寸 -->
        <el-table-column
          prop="size"
          label="尺寸"
          width="100"
          show-overflow-tooltip
        />

        <!-- 品相分数 -->
        <el-table-column
          prop="gradeScore"
          label="品相分数"
          width="100"
          align="center"
        />

        <!-- 客户姓名 -->
        <el-table-column
          prop="customerName"
          label="姓名 / 网名"
          width="120"
          show-overflow-tooltip
        />

        <!-- 费用 -->
        <el-table-column
          prop="fee"
          label="费用"
          width="80"
          align="center"
        />

        <!-- 审核状态 -->
        <el-table-column
          prop="auditStatus"
          label="审核状态"
          width="100"
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="row.auditStatus === 1 ? 'success' : 'warning'">
              {{ row.auditStatus === 1 ? '已审核' : '未审核' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="pagination.total"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>

    <!-- 打印标签预览 -->
<!--    <PrintLabels
      v-model="showPrintPreview"
      :print-data="printData"
      @confirm-print="handleConfirmPrint"
    />-->

    <!-- 增强预览组件 -->
    <EnhancedPreview
      v-model="showEnhancedPreview"
      :print-data="printData"
      @confirm-print="handleConfirmPrint"
      @refresh="handlePreviewRefresh"
    />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Refresh } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus/es';

// 组件导入
import SearchComponent from './components/search.vue';
import PrintConfig from './components/print-config.vue';
import PrintLabels from './components/print-labels.vue';
import EnhancedPreview from './components/EnhancedPreview.vue';

// API导入
import {
  queryPrintCoins,
  generatePrintData,
  executePrint
} from './api';

// 响应式数据
const searchRef = ref();
const printConfigRef = ref();
const tableRef = ref();
const loading = ref(false);
const coinList = ref([]);
const selectedCoins = ref([]);
const showPrintPreview = ref(false);
const showEnhancedPreview = ref(false);
const printData = ref(null);
const currentLabelType = ref(null);

// 分页数据
const pagination = reactive({
  current: 1,
  size: 20,
  total: 0
});

// 查询参数
const searchParams = ref({});

// 处理查询
const handleSearch = (params) => {
  searchParams.value = params;
  pagination.current = 1;
  loadCoinList();
};

// 处理重置
const handleReset = () => {
  searchParams.value = {};
  pagination.current = 1;
  loadCoinList();
};

// 处理刷新
const handleRefresh = () => {
  loadCoinList();
};

// 处理选择变化
const handleSelectionChange = (selection) => {
  selectedCoins.value = selection;
  // 重置打印权限
  printConfigRef.value?.resetCanPrint();
};

// 处理分页大小变化
const handleSizeChange = (size) => {
  pagination.size = size;
  pagination.current = 1;
  loadCoinList();
};

// 处理当前页变化
const handleCurrentChange = (current) => {
  pagination.current = current;
  loadCoinList();
};

// 加载钱币列表
const loadCoinList = async () => {
  loading.value = true;

  try {
    const params = {
      ...searchParams.value,
      current: pagination.current,
      size: pagination.size
    };

    const result = await queryPrintCoins(params);
    coinList.value = result.list || [];
    pagination.total = result.count || 0;

    // 清空选择
    selectedCoins.value = [];
    tableRef.value?.clearSelection();

  } catch (error) {
    EleMessage.error('查询失败：' + error.message);
    coinList.value = [];
    pagination.total = 0;
  } finally {
    loading.value = false;
  }
};

// 处理审核检测
const handleAuditCheck = (result) => {
  console.log('审核检测结果:', result);
};

// 处理标签打印
const handlePrintLabel = async (params) => {
  try {
    const data = await generatePrintData(params);
    printData.value = data;

    // 默认使用增强预览
    showEnhancedPreview.value = true;
  } catch (error) {
    EleMessage.error('生成打印数据失败：' + error.message);
  }
};

// 处理白标打印
const handlePrintWhite = async (params) => {
  try {
    const data = await generatePrintData({
      ...params,
      printType: 'white'
    });
    printData.value = data;
    showEnhancedPreview.value = true;
  } catch (error) {
    EleMessage.error('生成白标打印数据失败：' + error.message);
  }
};

// 处理确认打印
const handleConfirmPrint = async (printParams) => {
  try {
    const blob = await executePrint(printParams);

    // 创建下载链接
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `标签打印_${new Date().getTime()}.pdf`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);

    EleMessage.success('打印文件已生成');
    showPrintPreview.value = false;
    showEnhancedPreview.value = false;
  } catch (error) {
    EleMessage.error('打印失败：' + error.message);
  }
};

// 处理预览刷新
const handlePreviewRefresh = () => {
  console.log('预览已刷新');
  // 可以在这里添加刷新逻辑，比如重新获取数据
};

// 组件挂载时加载数据
onMounted(() => {
  // loadCoinList();
});
</script>

<style scoped>
.batch-print-page {
  padding: 16px;
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
}

:deep(.el-table) {
  font-size: 14px;
}

:deep(.el-table th) {
  background-color: #fafafa;
}
</style>
