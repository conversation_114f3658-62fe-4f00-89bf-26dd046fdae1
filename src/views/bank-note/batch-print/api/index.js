import request from '@/utils/request.js';

/**
 * 分页查询待打印钱币列表
 * @param {Object} params - 查询参数
 */
export async function queryPrintCoins(params) {
  const res = await request.get('/batch-print/coins', { params });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 检查钱币审核状态
 * @param {Array} coinIds - 钱币ID列表
 */
export async function checkCoinAuditStatus(coinIds) {
  const res = await request.post('/batch-print/check-audit', { coinIds });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}



/**
 * 生成打印数据（支持自定义模板）
 */
export async function generatePrintData(params) {
  const res = await request.post('/batch-print/generate', params);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 生成模拟打印数据
 * @param {Object} params - 打印参数
 */
function generateMockPrintData(params) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve({
        totalCount: params.coinIds?.length || 0,
        labelTypeName: '标准标签',
        conversionTypeName: '默认转换',
        coinIds: params.coinIds || [],
        items: (params.coinIds || []).map((id, index) => ({
          id,
          bankName: '中国人民银行',
          yearInfo: '1996年',
          coinName: index % 4 === 0 ? '壹角' : index % 4 === 1 ? '壹圆' : index % 4 === 2 ? '伍角' : '贰角',
          additionalInfo: index % 3 === 0 ? '黄金甲 中间约' : index % 3 === 1 ? '白幽灵' : '民族人物头像',
          serialNumber: `XB-${String(******** + index).padStart(8, '0')}`,
          version: '民族人物头像',
          gradeScore: 68 + (index % 5),
          gradeLevel: 'Superb Gem Unc',
          customerName: `客户${index + 1}`,
          printStatus: '待打印'
        }))
      });
    }, 500);
  });
}

/**
 * 获取打印预览数据
 * @param {Object} params - 预览参数
 */
export async function getPrintPreview(params) {
  const res = await request.post('/batch-print/preview', params);
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 执行打印
 * @param {Object} params - 打印参数
 */
export async function executePrint(params) {
  const res = await request.post('/batch-print/execute', params, {
    responseType: 'blob'
  });
  return res.data;
}

/**
 * 获取钱币类型选项
 */
export async function getCoinTypes() {
  const res = await request.get('/batch-print/coin-types');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取老系统兼容格式的打印数据
 * @param {string} service - 服务类型
 * @param {string} ids - 钱币ID列表（逗号分隔）
 * @param {number} conversion - 转换类型
 */
export async function getLegacyPrintData(service, ids, conversion = 0) {
  const res = await request.get(`/batch-print/printData/${service}`, {
    params: { ids, conversion }
  });
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}

/**
 * 获取标签模板列表
 */
export async function getTemplateList() {
  const res = await request.get('/label-design/templates');
  if (res.data.code === 0) {
    return res.data.data;
  }
  return Promise.reject(new Error(res.data.message));
}
