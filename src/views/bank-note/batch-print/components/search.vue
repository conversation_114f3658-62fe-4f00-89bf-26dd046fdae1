<template>
  <div class="search-container">
    <el-card shadow="never">
      <template #header>
        <div class="card-header">
          <span>查询条件</span>
          <el-button
            type="text"
            @click="toggleExpanded"
            :icon="expanded ? ArrowUp : ArrowDown"
          >
            {{ expanded ? '收起' : '展开' }}
          </el-button>
        </div>
      </template>

      <el-collapse-transition>
        <div v-show="expanded" class="search-form">
          <el-form
            ref="formRef"
            :model="form"
            label-width="100px"
            :inline="false"
          >
            <el-row :gutter="20">
              <!-- 钱币编号 -->
              <el-col :span="8">
                <el-form-item label="钱币编号">
                  <el-input
                    v-model="form.coinNumbers"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入钱币编号，多个编号请换行分隔"
                    maxlength="1000"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>

              <!-- 送评单号 -->
              <el-col :span="8">
                <el-form-item label="送评单号">
                  <el-input
                    v-model="form.sendformNumbers"
                    type="textarea"
                    :rows="3"
                    placeholder="请输入送评单号，多个单号请换行分隔"
                    maxlength="1000"
                    show-word-limit
                  />
                </el-form-item>
              </el-col>

              <!-- 钱币类型 -->
              <el-col :span="8">
                <el-form-item label="钱币类型">
                  <el-select
                    v-model="form.coinType"
                    placeholder="请选择钱币类型"
                    clearable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in coinTypeOptions"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <!-- 操作按钮 -->
              <el-col :span="8">
                <el-form-item>
                  <el-button
                    type="primary"
                    :icon="Search"
                    @click="handleSearch"
                    :loading="loading"
                  >
                    查询
                  </el-button>
                  <el-button
                    :icon="Refresh"
                    @click="handleReset"
                  >
                    重置
                  </el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </el-collapse-transition>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { Search, Refresh, ArrowUp, ArrowDown } from '@element-plus/icons-vue';
import { getCoinTypes } from '@/views/bank-note/batch-print/api/index.js';

const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['search', 'reset']);

// 响应式数据
const formRef = ref();
const expanded = ref(true);

// 表单数据
const form = reactive({
  coinNumbers: '',
  sendformNumbers: '',
  coinType: ''
});

// 选项数据
const coinTypeOptions = ref([]);

// 切换展开状态
const toggleExpanded = () => {
  expanded.value = !expanded.value;
};

// 处理查询
const handleSearch = () => {
  // 处理钱币编号和送评单号的换行分隔
  const searchParams = {
    ...form,
    coinNumbers: form.coinNumbers ? form.coinNumbers.split('\n').filter(item => item.trim()) : [],
    sendformNumbers: form.sendformNumbers ? form.sendformNumbers.split('\n').filter(item => item.trim()) : []
  };

  emit('search', searchParams);
};

// 处理重置
const handleReset = () => {
  formRef.value?.resetFields();
  Object.keys(form).forEach(key => {
    form[key] = '';
  });
  emit('reset');
};

// 加载钱币类型选项
const loadCoinTypes = async () => {
  try {
    const data = await getCoinTypes();
    coinTypeOptions.value = data || [
      { label: '纸币', value: '纸币' },
      { label: '古钱币', value: '古钱币' },
      { label: '机制币', value: '机制币' },
      { label: '银锭', value: '银锭' }
    ];
  } catch (error) {
    console.error('加载钱币类型失败:', error);
    // 使用默认选项
    coinTypeOptions.value = [
      { label: '纸币', value: '纸币' },
      { label: '古钱币', value: '古钱币' },
      { label: '机制币', value: '机制币' },
      { label: '银锭', value: '银锭' }
    ];
  }
};

// 组件挂载时加载数据
onMounted(() => {
  loadCoinTypes();
});

// 暴露方法给父组件
defineExpose({
  getFormData: () => form,
  resetForm: handleReset
});
</script>

<style scoped>
.search-container {
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-form {
  padding-top: 8px;
}

:deep(.el-textarea__inner) {
  resize: vertical;
  min-height: 80px;
}

:deep(.el-form-item) {
  margin-bottom: 16px;
}
</style>
