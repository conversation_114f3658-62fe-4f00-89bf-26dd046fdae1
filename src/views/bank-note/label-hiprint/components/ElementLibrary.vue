<template>
  <div class="element-library">
    <div class="library-header">
      <h4>元素库</h4>
      <el-button size="small" @click="refreshFields" :loading="loading">
        <el-icon><Refresh /></el-icon>
      </el-button>
    </div>

    <!-- 基础元素 -->
    <div class="element-group">
      <div class="group-header" @click="toggleGroup('basic')">
        <el-icon><CaretRight :class="{ 'expanded': expandedGroups.basic }" /></el-icon>
        <span>基础元素</span>
      </div>
      <el-collapse-transition>
        <div v-show="expandedGroups.basic" class="group-content">
          <div
            v-for="(element, key) in basicElements"
            :key="key"
            class="element-item ep-draggable-item"
            :tid="getHiprintTid(element.template.type)"
          >
            <el-icon class="element-icon">
              <component :is="element.icon" />
            </el-icon>
            <span class="element-name">{{ element.name }}</span>
          </div>
        </div>
      </el-collapse-transition>
    </div>

    <!-- 数据字段部分已移除，只保留基础元素 -->
  </div>
</template>

<script setup>
import { ref, reactive } from 'vue';
import {
  CaretRight,
  Refresh,
  EditPen,
  Picture,
  Postcard,
  Grid,
  Minus,
  Crop
} from '@element-plus/icons-vue';
import { elementTemplates } from '@/utils/hiprint-config';

const props = defineProps({
  onElementDrag: {
    type: Function,
    default: () => {}
  }
});

const emit = defineEmits(['element-drag-start', 'element-drag-end']);

// 响应式数据
const loading = ref(false);
const expandedGroups = reactive({
  basic: true
});

// 基础元素定义
const basicElements = {
  text: {
    name: '文本',
    icon: EditPen,
    template: elementTemplates.text
  },
  image: {
    name: '图片',
    icon: Picture,
    template: elementTemplates.image
  },
  barcode: {
    name: '条形码',
    icon: Postcard,
    template: elementTemplates.barcode
  },
  qrcode: {
    name: '二维码',
    icon: Grid,
    template: elementTemplates.qrcode
  },
  line: {
    name: '线条',
    icon: Minus,
    template: elementTemplates.line
  },
  rect: {
    name: '矩形',
    icon: Crop,
    template: elementTemplates.rect
  }
};

// 方法定义
const toggleGroup = (groupName) => {
  expandedGroups[groupName] = !expandedGroups[groupName];
};

// 获取hiprint默认的tid
const getHiprintTid = (elementType) => {
  // 映射自定义元素类型到hiprint默认类型
  const tidMap = {
    'text': 'defaultModule.text',
    'image': 'defaultModule.image',
    'barcode': 'defaultModule.barcode',
    'qrcode': 'defaultModule.qrcode',
    'hline': 'defaultModule.hline',
    'rect': 'defaultModule.rect'
  };

  return tidMap[elementType] || 'defaultModule.text';
};



// 刷新方法（保留以备将来使用）
const refreshFields = () => {
  console.log('基础元素库无需刷新');
};
</script>

<style scoped>
.element-library {
  height: 100%;
  overflow-y: auto;
  padding: 16px;
  background: #f8f9fa;
}

.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e0e0e0;
}

.library-header h4 {
  margin: 0;
  font-size: 14px;
  font-weight: 600;
  color: #333;
}

.element-group {
  margin-bottom: 16px;
}

.group-header {
  display: flex;
  align-items: center;
  padding: 8px 0;
  cursor: pointer;
  font-weight: 600;
  color: #555;
  border-bottom: 1px solid #eee;
}

.group-header:hover {
  color: #409eff;
}

.group-header .el-icon {
  margin-right: 8px;
  transition: transform 0.3s;
}

.group-header .el-icon.expanded {
  transform: rotate(90deg);
}

.group-content {
  padding: 8px 0;
}

.element-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  margin-bottom: 4px;
  background: #fff;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  cursor: grab;
  transition: all 0.2s;
  user-select: none;
}

.element-item:hover {
  background: #e6f7ff;
  border-color: #409eff;
  transform: translateX(2px);
}

.element-item:active {
  cursor: grabbing;
}

.element-icon {
  margin-right: 8px;
  color: #666;
}

.element-name {
  flex: 1;
  font-size: 12px;
  color: #333;
}

/* 字段相关样式已移除 */

/* 拖拽时的样式 */
.element-item.dragging {
  opacity: 0.5;
  transform: rotate(5deg);
}

/* hiprint 拖拽元素样式 */
.ep-draggable-item {
  position: relative;
}

.ep-draggable-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: transparent;
  pointer-events: none;
}

.ep-draggable-item:hover::after {
  background: rgba(64, 158, 255, 0.1);
  border: 1px dashed #409eff;
}

/* 滚动条样式 */
.element-library::-webkit-scrollbar {
  width: 6px;
}

.element-library::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.element-library::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.element-library::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
