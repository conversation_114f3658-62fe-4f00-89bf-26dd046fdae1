<template>
  <ele-drawer
    :size="'90%'"
    title="修改送评单"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <!-- 送评单基本信息 -->
    <div class="sendform-info">
      <div class="section-title">
        <h4>送评单信息</h4>
      </div>

      <el-form
        ref="sendformFormRef"
        :model="sendformData"
        label-width="100px"
        size="small"
      >
        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="送评单号">
              <el-input v-model="sendformData.sendnum" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="时间">
              <el-input v-model="sendformData.inupttime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="保价">
              <el-input-number
                v-model="sendformData.safemoney"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="送评人">
              <el-input v-model="sendformData.rname" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="网名">
              <el-input v-model="sendformData.nickname" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="电话">
              <el-input v-model="sendformData.rphone" />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="快递费">
              <el-input-number
                v-model="sendformData.expressFee"
                :precision="2"
                :min="0"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="其他费用">
              <el-input-number
                v-model="sendformData.otherFee"
                :precision="2"
                :min="0"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="总费用">
              <el-input :value="totalFee" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="8">
            <el-form-item label="收评号">
              <el-input v-model="sendformData.rcode" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="财务核对">
              <el-radio-group v-model="sendformData.ifyou">
                <el-radio :value="1">已核对</el-radio>
                <el-radio :value="0">未核对</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="收货方式">
              <el-radio-group v-model="sendformData.rtype">
                <el-radio :value="1">上门自提</el-radio>
                <el-radio :value="2">快递邮寄</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="16">
          <el-col :span="24">
            <el-form-item label="收货地址">
              <el-input v-model="sendformData.addr" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>

    <!-- 钱币列表和详情 -->
    <div class="coin-section">
      <div class="section-title">
        <h4>钱币信息</h4>
      </div>

      <el-row :gutter="20">
        <!-- 左侧钱币列表 -->
        <el-col :span="8">
          <div class="coin-list">
            <el-table
              :data="coinList"
              @row-click="selectCoin"
              highlight-current-row
              :current-row-key="currentCoin?.id"
              :row-key="(row) => row.id"
              size="small"
              max-height="600"
            >
              <el-table-column prop="diyCode" label="送评条码" width="120" />
              <el-table-column prop="coinName1" label="钱币名称" />
              <el-table-column prop="coinType" label="类型" width="80">
                <template #default="{ row }">
                  {{ coinTypeMap[row.coinType] || row.coinType }}
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>

        <!-- 右侧钱币详情 -->
        <el-col :span="16">
          <div v-if="currentCoin" class="coin-detail">
            <!-- 切换按钮 -->
            <div class="coin-nav">
              <el-button @click="prevCoin" :disabled="currentIndex === 0">
                <el-icon><ArrowLeft /></el-icon>
                上一个
              </el-button>
              <span class="coin-index"
                >{{ currentIndex + 1 }} / {{ coinList.length }}</span
              >
              <el-button
                @click="nextCoin"
                :disabled="currentIndex === coinList.length - 1"
              >
                下一个
                <el-icon><ArrowRight /></el-icon>
              </el-button>
            </div>

            <el-form
              ref="coinFormRef"
              :model="currentCoin"
              label-width="100px"
              size="small"
            >
              <!-- 钱币基本信息 -->
              <div class="detail-section">
                <h5>钱币基本信息</h5>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="类型">
                      <el-input
                        :value="coinTypeMap[currentCoin.coinType]"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="编号">
                      <el-input v-model="currentCoin.serialNumber" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="名称1">
                      <el-input v-model="currentCoin.coinName1" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="名称2">
                      <el-input v-model="currentCoin.coinName2" />
                    </el-form-item>
                  </el-col>
                </el-row>

                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="年代">
                      <el-input v-model="currentCoin.yearInfo" />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="版别">
                      <dict-data
                        v-model="currentCoin.version"
                        code="edition"
                        type="select"
                        placeholder="请选择版别"
                        filterable
                        clearable
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 古钱币专有字段 -->
                <template v-if="isAncientCoin">
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="等级">
                        <el-input v-model="currentCoin.grade" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="材质">
                        <el-input v-model="currentCoin.material" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="尺寸">
                        <el-input v-model="currentCoin.coinSize" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="重量">
                        <el-input-number
                          v-model="currentCoin.coinWeight"
                          :precision="3"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>

                <!-- 机制币专有字段 -->
                <template v-if="isMachineCoin">
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="面值">
                        <el-input v-model="currentCoin.faceValue" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="材质">
                        <el-input v-model="currentCoin.material" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="尺寸">
                        <el-input v-model="currentCoin.coinSize" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="重量">
                        <el-input-number
                          v-model="currentCoin.coinWeight"
                          :precision="3"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>

                <!-- 银锭专有字段 -->
                <template v-if="isSilverIngot">
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="年号">
                        <el-input v-model="currentCoin.reign" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="地区">
                        <el-input v-model="currentCoin.region" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="税种">
                        <el-input v-model="currentCoin.taxType" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="面值">
                        <el-input v-model="currentCoin.faceValue" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="材质">
                        <el-input v-model="currentCoin.material" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="尺寸">
                        <el-input v-model="currentCoin.coinSize" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="重量">
                        <el-input-number
                          v-model="currentCoin.coinWeight"
                          :precision="3"
                          style="width: 100%"
                        />
                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>

                <!-- 纸币专有字段 -->
                <template v-if="isBanknote">
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="特殊标签">
                        <el-input
                          v-model="currentCoin.specialLabel"
                          placeholder="请输入特殊标签"
                        />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="银行名称">
                        <el-input v-model="currentCoin.bankName" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <el-form-item label="目录">
                        <el-input v-model="currentCoin.catalog" />
                      </el-form-item>
                    </el-col>
                    <el-col :span="12">
                      <el-form-item label="地区">
                        <el-input v-model="currentCoin.region" />
                      </el-form-item>
                    </el-col>
                  </el-row>
                  <el-row :gutter="16">
                    <el-col :span="24">
                      <el-form-item label="钱币备注">
                        <el-input
                          v-model="currentCoin.coinRemark"
                          placeholder="请输入钱币备注"
                        />

                      </el-form-item>
                    </el-col>
                  </el-row>
                </template>
              </div>

              <!-- 真伪鉴定 -->
              <div class="detail-section">
                <h5>真伪鉴定</h5>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="真伪">
                      <dict-data
                        v-model="currentCoin.authenticity"
                        code="authenticity"
                        type="select"
                        placeholder="请选择真伪"
                        clearable
                        style="width: 100%"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

              </div>
              <!-- 评级打分（不可改） -->
              <div class="detail-section">
                <h5>评级打分（不可改）</h5>
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="品相">
                      <dict-data
                        v-model="currentCoin.gradeScore"
                        code="gradeScore"
                        type="select"
                        placeholder="请选择品相"
                        filterable
                        clearable
                        style="width: 100%"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="">
                      <dict-data
                        v-model="currentCoin.specialMark"
                        code="specialMark"
                        type="checkbox"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 缺陷修补（不可改） -->
              <div class="detail-section">
                <h5>缺陷修补（不可改）</h5>
                <el-row :gutter="16">
                  <el-col :span="24">
                    <el-form-item label="评分备注">
                      <el-input
                        v-model="currentCoin.scoreRemarks"
                        type="textarea"
                        :rows="2"
                        placeholder="分数备注"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 其他信息 -->
              <div class="detail-section">
                <h5>其他信息</h5>
                <!-- 盒子类型 -->
                <!--                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="数量">
                      <el-input-number
                        v-model="currentCoin.quantity"
                        :min="1"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="加急">
                      <el-select v-model="currentCoin.urgent" style="width: 100%">
                        <el-option :value="0" label="普通" />
                        <el-option :value="1" label="加急" />
                      </el-select>
                    </el-form-item>
                  </el-col>
                </el-row>-->

                <!-- 价格费用 -->
                <el-row :gutter="16">
                  <!--                  <el-col :span="8">
                    <el-form-item label="标准价格">
                      <el-input-number
                        v-model="currentCoin.standardPrice"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>-->
                  <el-col :span="8">
                    <el-form-item label="国际价格">
                      <el-input-number
                        v-model="currentCoin.internationalPrice"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="折扣">
                      <el-input-number
                        v-model="currentCoin.discount"
                        :precision="1"
                        :min="0"
                        :max="100"
                        style="width: 100%"
                        disabled
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="费用">
                      <el-input-number
                        v-model="currentCoin.gradeFee"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
                <el-row :gutter="16">
                  <el-col :span="8">
                    <el-form-item label="预估价格">
                      <el-input-number
                        v-model="currentCoin.estimatedPrice"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="8">
                    <el-form-item label="盒子费">
                      <el-input-number
                        v-model="currentCoin.boxFee"
                        :precision="2"
                        :min="0"
                        style="width: 100%"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>

                <!-- 备注信息 -->
                <el-row :gutter="16">
                  <el-col :span="12">
                    <el-form-item label="对外备注">
                      <el-input
                        v-model="currentCoin.externalNote"
                        type="textarea"
                        :rows="2"
                      />
                    </el-form-item>
                  </el-col>
                  <el-col :span="12">
                    <el-form-item label="对内备注">
                      <el-input
                        v-model="currentCoin.internalNote"
                        type="textarea"
                        :rows="2"
                      />
                    </el-form-item>
                  </el-col>
                </el-row>
              </div>

              <!-- 图片 -->
              <div class="detail-section">
                <h5>图片</h5>
                <div class="coin-images">
                  <ele-upload-list
                    v-model="coinImages"
                    :limit="10"
                    :drag="true"
                    :multiple="true"
                    accept="image/*"
                    :item-style="{ width: '120px', height: '120px' }"
                    :button-style="{ width: '120px', height: '120px' }"
                    @upload="onImageUpload"
                    @remove="onImageRemove"
                  >
                    <template #default>
                      <div class="upload-placeholder">
                        <el-icon :size="24"><Upload /></el-icon>
                        <div>点击或拖拽上传图片</div>
                      </div>
                    </template>
                  </ele-upload-list>
                </div>
              </div>
            </el-form>
          </div>

          <el-empty v-else description="请选择钱币查看详情" />
        </el-col>
      </el-row>
    </div>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button
        size="small"
        type="primary"
        :loading="loading"
        @click="saveAll"
      >
        保存送评单
      </el-button>
    </template>
  </ele-drawer>
</template>

<script setup>
  import { ref, computed, watch, onMounted } from 'vue';
  import {
    ArrowLeft,
    ArrowRight,
    Upload,
    Delete
  } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { ElMessageBox } from 'element-plus';
  import { getSendformDetail, saveOrUpdateSendform } from '../api';
  import { uploadFile } from '@/api/system/file';
  import { useCoinTypes } from '@/composables/use-coin-types';


  const emit = defineEmits(['done', 'update:modelValue']);

  const props = defineProps({
    modelValue: Boolean,
    sendnum: String
  });

  /** 送评单数据 */
  const sendformData = ref({});

  /** 钱币列表 */
  const coinList = ref([]);

  /** 当前选中的钱币 */
  const currentCoin = ref(null);

  /** 当前钱币索引 */
  const currentIndex = ref(0);

  /** 提交状态 */
  const loading = ref(false);

  /** 使用动态代码表替代硬编码映射 */
  const {
    coinTypeMap,
    getCoinTypeName,
    checkIsBanknote,
    checkIsAncientCoin,
    checkIsMachineCoin,
    checkIsSilverIngot
  } = useCoinTypes();



  /** 总费用计算 */
  const totalFee = computed(() => {
    const expressFee = Number(sendformData.value.expressFee) || 0;
    const otherFee = Number(sendformData.value.otherFee) || 0;
    // 计算所有钱币的费用总和
    const coinFees = coinList.value.reduce((sum, coin) => {
      return sum + (Number(coin.gradeFee) || 0);
    }, 0);
    return expressFee + otherFee + coinFees;
  });

  /** 使用动态代码表的钱币类型判断 */
  const isAncientCoin = computed(() => {
    return checkIsAncientCoin(currentCoin.value?.coinType);
  });

  const isMachineCoin = computed(() => {
    return checkIsMachineCoin(currentCoin.value?.coinType);
  });

  const isSilverIngot = computed(() => {
    return checkIsSilverIngot(currentCoin.value?.coinType);
  });

  const isBanknote = computed(() => {
    return checkIsBanknote(currentCoin.value?.coinType);
  });

  /** 图片数据处理 */
  const coinImages = computed({
    get() {
      if (!currentCoin.value?.coinImages) return [];

      // 处理不同的数据格式：字符串或数组
      let imageUrls = [];
      if (typeof currentCoin.value.coinImages === 'string') {
        // 如果是字符串，可能是单个URL或逗号分隔的多个URL
        imageUrls = currentCoin.value.coinImages
          .split(',')
          .filter((url) => url.trim());
      } else if (Array.isArray(currentCoin.value.coinImages)) {
        imageUrls = currentCoin.value.coinImages;
      }

      // 将URL数组转换为上传组件需要的格式
      return imageUrls.map((url, index) => ({
        id: `img_${index}`,
        url: url.trim(),
        name: `图片${index + 1}`,
        status: 'done',
        response: { url: url.trim() }
      }));
    },
    set(value) {
      if (!currentCoin.value) return;

      // 将上传组件格式转换为URL数组
      const urls = value
        .filter(
          (item) => item.status === 'done' && (item.url || item.response?.url)
        )
        .map((item) => item.url || item.response?.url);

      // 根据后端期望的格式保存：如果只有一个URL就保存为字符串，多个URL用逗号分隔
      if (urls.length === 0) {
        currentCoin.value.coinImages = null;
      } else if (urls.length === 1) {
        currentCoin.value.coinImages = urls[0];
      } else {
        currentCoin.value.coinImages = urls.join(',');
      }
    }
  });

  /** 图片上传处理 */
  const onImageUpload = async (item) => {
    if (!item.file) return;

    // 文件类型检查
    if (!item.file.type.startsWith('image/')) {
      EleMessage.error('只能上传图片文件');
      return;
    }

    // 文件大小检查（限制为5MB）
    if (item.file.size / 1024 / 1024 > 5) {
      EleMessage.error('图片大小不能超过5MB');
      return;
    }

    try {
      // 设置上传状态
      item.status = 'uploading';

      // 调用上传API
      const result = await uploadFile(item.file);

      // 添加调试信息
      console.log('Upload result:', result);

      // 解析返回的数据
      let fileData;
      if (Array.isArray(result)) {
        fileData = result[0];
      } else if (typeof result === 'string') {
        // 如果返回的是JSON字符串，需要解析
        try {
          const parsed = JSON.parse(result);
          fileData = Array.isArray(parsed) ? parsed[0] : parsed;
        } catch (e) {
          console.error('Failed to parse result:', result);
          throw new Error('上传返回数据格式错误');
        }
      } else {
        fileData = result;
      }

      console.log('Parsed fileData:', fileData);

      // 构建图片URL - 使用正确的API路径
      const imageUrl = `/api/file/inline/${fileData.id}`;
      console.log('Generated imageUrl:', imageUrl);

      // 上传成功
      item.status = 'done';
      item.url = imageUrl;
      item.response = { url: imageUrl, fileData };

      EleMessage.success('图片上传成功');

      // 更新钱币图片数据
      if (!currentCoin.value.coinImages) {
        currentCoin.value.coinImages = imageUrl;
      } else {
        // 如果已有图片，追加新的URL
        if (typeof currentCoin.value.coinImages === 'string') {
          currentCoin.value.coinImages =
            currentCoin.value.coinImages + ',' + imageUrl;
        } else if (Array.isArray(currentCoin.value.coinImages)) {
          currentCoin.value.coinImages.push(imageUrl);
        }
      }
    } catch (error) {
      item.status = 'error';
      EleMessage.error('图片上传失败：' + (error.message || '未知错误'));
      console.error('Upload error:', error);
    }
  };

  /** 图片删除处理 */
  const onImageRemove = (item) => {
    ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
      type: 'warning',
      confirmButtonText: '确定',
      cancelButtonText: '取消'
    })
      .then(() => {
        // 从钱币图片中移除
        if (currentCoin.value?.coinImages) {
          const imageUrl = item.url || item.response?.url;

          if (typeof currentCoin.value.coinImages === 'string') {
            // 如果是字符串格式，按逗号分隔处理
            const urls = currentCoin.value.coinImages
              .split(',')
              .filter((url) => url.trim() !== imageUrl);
            currentCoin.value.coinImages =
              urls.length > 0 ? urls.join(',') : null;
          } else if (Array.isArray(currentCoin.value.coinImages)) {
            // 如果是数组格式
            const index = currentCoin.value.coinImages.indexOf(imageUrl);
            if (index > -1) {
              currentCoin.value.coinImages.splice(index, 1);
            }
          }
        }
        EleMessage.success('图片删除成功');
      })
      .catch(() => {
        // 用户取消删除
      });
  };

  /** 选择钱币 */
  const selectCoin = (row, index) => {
    currentCoin.value = {
      ...row,
      // 确保 specialMark 是数组
      specialMark: Array.isArray(row.specialMark) ? row.specialMark : [],
      // coinImages 保持原始格式（字符串或数组）
      coinImages: row.coinImages || null
    };
    currentIndex.value = coinList.value.findIndex((item) => item.id === row.id);
  };

  /** 上一个钱币 */
  const prevCoin = () => {
    if (currentIndex.value > 0) {
      currentIndex.value--;
      const coin = coinList.value[currentIndex.value];
      currentCoin.value = {
        ...coin,
        // 确保 specialMark 是数组
        specialMark: Array.isArray(coin.specialMark) ? coin.specialMark: [],
        // coinImages 保持原始格式
        coinImages: coin.coinImages || null
      };
    }
  };

  /** 下一个钱币 */
  const nextCoin = () => {
    if (currentIndex.value < coinList.value.length - 1) {
      currentIndex.value++;
      const coin = coinList.value[currentIndex.value];
      currentCoin.value = {
        ...coin,
        // 确保 specialMark 是数组
        specialMark: Array.isArray(coin.specialMark) ? coin.specialMark : [],
        // coinImages 保持原始格式
        coinImages: coin.coinImages || null
      };
    }
  };

  /** 保存所有数据 */
  const saveAll = async () => {
    loading.value = true;

    try {
      // 确保当前编辑的钱币数据已同步到列表中
      if (currentCoin.value) {
        const index = coinList.value.findIndex(
          (item) => item.id === currentCoin.value.id
        );
        if (index >= 0) {
          coinList.value[index] = { ...currentCoin.value };
        }
      }

      // 准备提交数据
      const submitData = {
        sendform: sendformData.value,
        ancientCoins: coinList.value.filter((item) =>
          checkIsAncientCoin(item.coinType)
        ),
        machineCoins: coinList.value.filter((item) =>
          checkIsMachineCoin(item.coinType)
        ),
        silverIngots: coinList.value.filter((item) =>
          checkIsSilverIngot(item.coinType)
        ),
        banknotes: coinList.value.filter((item) =>
          checkIsBanknote(item.coinType)
        )
      };

      await saveOrUpdateSendform(submitData);
      EleMessage.success('送评单保存成功');
      emit('done');
      updateModelValue(false);
    } catch (error) {
      EleMessage.error(error.message || '保存失败');
    } finally {
      loading.value = false;
    }
  };

  /** 加载送评单详情 */
  const loadDetail = async () => {
    if (!props.sendnum) return;

    try {
      const data = await getSendformDetail(props.sendnum);
      sendformData.value = data.sendform || {};
      // 合并所有钱币并确保 specialMark 字段为数组，coinImages 保持原始格式
      const allCoins = [
        ...(data.ancientCoins || []),
        ...(data.machineCoins || []),
        ...(data.silverIngots || []),
        ...(data.banknotes || [])
      ].map((coin) => ({
        ...coin,
        // 确保 specialMark 是数组
        specialMark: Array.isArray(coin.specialMark) ? coin.specialMark : [],
        // coinImages 保持原始格式（字符串或数组）
        coinImages: coin.coinImages || null
      }));

      coinList.value = allCoins;

      // 默认选中第一个
      if (allCoins.length > 0) {
        selectCoin(allCoins[0], 0);
      }
    } catch (error) {
      EleMessage.error('加载送评单详情失败');
    }
  };

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emit('update:modelValue', value);
  };

  watch(
    () => props.modelValue,
    (val) => {
      if (val) {
        loadDetail();
      }
    }
  );

  // 组件挂载时的初始化
  onMounted(() => {
    // dict-data组件会自动加载码表数据，无需手动调用
  });
</script>

<style scoped>
  .sendform-info {
    margin-bottom: 20px;
    padding: 16px;
    background: #fafafa;
    border-radius: 4px;
  }

  .section-title {
    margin: 20px 0 16px 0;
    padding-bottom: 8px;
    border-bottom: 1px solid #ebeef5;
  }

  .section-title h4 {
    margin: 0;
    color: #303133;
    font-size: 16px;
    font-weight: 600;
  }

  .coin-section {
    margin-top: 20px;
  }

  .coin-list {
    border: 1px solid #ebeef5;
    border-radius: 4px;
  }

  .coin-detail {
    padding: 0 20px;
  }

  .coin-nav {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;
    padding-bottom: 10px;
    border-bottom: 1px solid #ebeef5;
  }

  .coin-index {
    font-size: 14px;
    color: #606266;
  }

  .detail-section {
    margin-bottom: 20px;
    padding: 16px;
    background: #f5f7fa;
    border-radius: 4px;
  }

  .detail-section h5 {
    margin: 0 0 16px 0;
    color: #303133;
    font-size: 14px;
    font-weight: 600;
  }

  .coin-images {
    display: flex;
    flex-wrap: wrap;
  }

  .upload-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    color: #8c939d;
    font-size: 12px;
  }

  .upload-placeholder .el-icon {
    margin-bottom: 8px;
  }

  :deep(.ele-upload-list) {
    gap: 10px;
  }

  :deep(.ele-upload-item) {
    border-radius: 6px;
    overflow: hidden;
  }

  :deep(.ele-upload-item img) {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  :deep(.ele-upload-button) {
    border: 2px dashed #d9d9d9;
    border-radius: 6px;
    background-color: #fafafa;
    transition: all 0.3s;
  }

  :deep(.ele-upload-button:hover) {
    border-color: #409eff;
    background-color: #f0f9ff;
  }

  :deep(.el-descriptions__label) {
    font-weight: 600;
    color: #606266;
  }

  :deep(.el-table__row) {
    cursor: pointer;
  }

  :deep(.el-table__row:hover) {
    background-color: #f5f7fa;
  }
</style>
