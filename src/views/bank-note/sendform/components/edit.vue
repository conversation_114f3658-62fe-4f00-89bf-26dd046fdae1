<template>
  <ele-drawer
    :size="'90%'"
    :title="isUpdate ? '编辑送评单' : '创建送评单'"
    :append-to-body="true"
    style="max-width: 100%"
    :destroy-on-close="true"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px' }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      label-width="120px"
      size="small"
      @submit.prevent
    >
      <div class="section-title">
        <h4>送评单信息</h4>
      </div>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="联系电话" prop="sendform.rphone">
            <el-input v-model="form.sendform.rphone" placeholder="请输入联系电话" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户名称" prop="sendform.rname">
            <el-input v-model="form.sendform.rname" placeholder="请输入用户名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="网名" prop="sendform.nickname">
            <el-input v-model="form.sendform.nickname" placeholder="请输入网名" disabled/>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="保价金额" prop="sendform.safemoney">
            <el-input-number
              v-model="form.sendform.safemoney"
              :precision="2"
              placeholder="请输入保价金额"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="加急" prop="sendform.urgenttype">
            <el-radio-group v-model="form.sendform.urgenttype">
              <el-radio :value="0">普通</el-radio>
              <el-radio :value="1">加急</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
<!--        <el-col :span="8">
          <el-form-item label="加急费" prop="sendform.urgentfee" v-if="form.sendform.urgenttype === 1">
            <el-input-number
              v-model="form.sendform.urgentfee"
              :precision="2"
              placeholder="请输入加急费"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>-->
                 <el-col :span="12">
           <el-form-item label="添加收评" prop="sendform.issms">
             <el-radio-group v-model="form.sendform.issms">
               <el-radio :value="1">是</el-radio>
               <el-radio :value="0">否</el-radio>
             </el-radio-group>
           </el-form-item>
         </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="收货类型" prop="sendform.rtype">
            <el-radio-group v-model="form.sendform.rtype">
              <el-radio :value="1">上门提货</el-radio>
              <el-radio :value="2">快递收寄</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="收货地址" prop="sendform.addr">
            <el-input
              v-model="form.sendform.addr"
              placeholder="请输入收货地址"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="内部备注" prop="sendform.sendmemo">
            <el-input
              v-model="form.sendform.sendmemo"
              type="textarea"
              :rows="3"
              placeholder="请输入内部备注"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="用户备注" prop="sendform.sendremark">
            <el-input
              v-model="form.sendform.sendremark"
              type="textarea"
              :rows="3"
              placeholder="请输入用户备注"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="收评号" prop="sendform.rcode">
            <el-input
              v-model="form.sendform.rcode"
              placeholder="请输入收评号"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 钱币信息 -->
      <div class="section-title">
        <div class="title-with-actions">
          <h4>钱币信息</h4>
          <div class="layout-actions">
            <el-radio-group v-model="layoutMode" size="small">
              <el-radio-button value="card">卡片视图</el-radio-button>
              <el-radio-button value="table">表格视图</el-radio-button>
            </el-radio-group>
          </div>
        </div>
      </div>

      <!-- 钱币类型切换 -->
      <el-tabs v-if="visibleCoinTypes.length > 0" v-model="activeCoinType" type="card">
        <el-tab-pane
          v-if="visibleCoinTypes.includes('banknote')"
          label="纸币"
          name="banknote"
        >
          <coin-detail-card
            v-if="layoutMode === 'card'"
            :table-data="form.banknotes"
            coin-type="banknote"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <coin-detail-table
            v-else
            :table-data="form.banknotes"
            coin-type="banknote"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <div style="margin-top: 16px;">
            <el-button type="primary" size="small" @click="addNewCoin('banknote')" icon="Plus">
              添加纸币
            </el-button>
          </div>
        </el-tab-pane>

        <el-tab-pane
          v-if="visibleCoinTypes.includes('ancientCoin')"
          label="古钱币"
          name="ancientCoin"
        >
          <coin-detail-card
            v-if="layoutMode === 'card'"
            :table-data="form.ancientCoins"
            coin-type="ancientCoin"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <coin-detail-table
            v-else
            :table-data="form.ancientCoins"
            coin-type="ancientCoin"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <div style="margin-top: 16px;">
            <el-button type="primary" size="small" @click="addNewCoin('ancientCoin')" icon="Plus">
              添加古钱币
            </el-button>
          </div>
        </el-tab-pane>

        <el-tab-pane
          v-if="visibleCoinTypes.includes('machineCoin')"
          label="机制币"
          name="machineCoin"
        >
          <coin-detail-card
            v-if="layoutMode === 'card'"
            :table-data="form.machineCoins"
            coin-type="machineCoin"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <coin-detail-table
            v-else
            :table-data="form.machineCoins"
            coin-type="machineCoin"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <div style="margin-top: 16px;">
            <el-button type="primary" size="small" @click="addNewCoin('machineCoin')" icon="Plus">
              添加机制币
            </el-button>
          </div>
        </el-tab-pane>

        <el-tab-pane
          v-if="visibleCoinTypes.includes('silverIngot')"
          label="银锭"
          name="silverIngot"
        >
          <coin-detail-card
            v-if="layoutMode === 'card'"
            :table-data="form.silverIngots"
            coin-type="silverIngot"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <coin-detail-table
            v-else
            :table-data="form.silverIngots"
            coin-type="silverIngot"
            @field-change="onCoinFieldChange"
            @delete="onCoinDelete"
          />
          <div style="margin-top: 16px;">
            <el-button type="primary" size="small" @click="addNewCoin('silverIngot')" icon="Plus">
              添加银锭
            </el-button>
          </div>
        </el-tab-pane>
      </el-tabs>

      <!-- 当没有可见的钱币类型时显示提示和添加按钮 -->
      <div v-if="visibleCoinTypes.length === 0" class="empty-hint">
        <p>暂无钱币数据，请选择要添加的钱币类型：</p>
        <div style="margin-top: 16px;">
          <el-button type="primary" size="small" @click="addNewCoin('banknote')" icon="Plus">
            添加纸币
          </el-button>
          <el-button type="primary" size="small" @click="addNewCoin('ancientCoin')" icon="Plus" style="margin-left: 8px;">
            添加古钱币
          </el-button>
          <el-button type="primary" size="small" @click="addNewCoin('machineCoin')" icon="Plus" style="margin-left: 8px;">
            添加机制币
          </el-button>
          <el-button type="primary" size="small" @click="addNewCoin('silverIngot')" icon="Plus" style="margin-left: 8px;">
            添加银锭
          </el-button>
        </div>
      </div>
    </el-form>

    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button size="small" type="primary" plain :loading="loading" @click="onSubmit">
        {{ isUpdate ? '保存' : '创建' }}
      </el-button>
    </template>

    <!-- 增加钱币对话框（编辑模式） -->
    <add-coin-dialog
      v-if="isUpdate"
      v-model="addCoinVisible"
      :sendnum="form.sendform.sendnum"
      @done="handleAddCoinDone"
    />

    <!-- 本地添加钱币对话框（创建模式） -->
    <local-add-coin-dialog
      v-if="!isUpdate"
      v-model="localAddCoinVisible"
      :default-coin-type="activeCoinType"
      @confirm="handleLocalAddCoin"
    />
  </ele-drawer>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useFormData } from '@/utils/use-form-data';
import { EleMessage } from "ele-admin-plus";
import { useUserStore } from '@/store/modules/user';
import { saveOrUpdateSendform, getSendformDetail } from '../api';
import CoinDetailCard from './CoinDetailCard.vue';
import CoinDetailTable from './CoinDetailTable.vue';
import LocalAddCoinDialog from './LocalAddCoinDialog.vue';

const emit = defineEmits(['done', 'update:modelValue']);

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /** 修改回显的数据 */
  data: Object
});

/** 是否是修改 */
const isUpdate = computed(() => !!props.data);

/** 用户store */
const userStore = useUserStore();

/** 获取当前用户的网名 */
const getCurrentUserNickname = () => {
  const userInfo = userStore.info;
  if (!userInfo) {
    return '';
  }

  // 如果用户信息中有onlineName字段，直接使用
  if (userInfo.onlineName) {
    return userInfo.onlineName;
  }
  const userName = userInfo.username || userInfo.userName;
  return userName;
};

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields, assignFields] = useFormData({
  sendform: {
    id: undefined,
    sendnum: '',
    rphone: '',
    rname: '',
    nickname: '',
    safemoney: null,
    rtype: 2,
    addr: '',
    urgenttype: 0, // 加急类型：0-普通，1-加急
    urgentfee: null, // 加急费
    issms: 1, // 是否短信：1-是，0-否（用于替代添加收评功能）
    sendmemo: '', // 内部备注
    sendremark: '', // 用户备注
    rcode: '' // 收评号（收货代码）
  },
  ancientCoins: [],
  machineCoins: [],
  silverIngots: [],
  banknotes: []
});

/** 表单验证规则 */
const rules = {
  'sendform.rphone': [
    { required: true, message: '请输入联系电话', trigger: 'blur' }
  ],
  'sendform.rname': [
    { required: true, message: '请输入真实姓名', trigger: 'blur' }
  ]
};

/** 提交状态 */
const loading = ref(false);

/** 当前激活的钱币类型 */
const activeCoinType = ref('banknote');

/** 是否已经添加过钱币（新建模式下用于判断是否显示所有tabs） */
const hasAddedCoins = ref(false);

/** 布局模式：card（卡片）或 table（表格） */
const layoutMode = ref('card');

/** 可见的钱币类型（只显示有数据的类型） */
const visibleCoinTypes = computed(() => {
  const types = [];

  // 检查每种钱币类型是否有数据（包括未被删除的数据）
  if (form.banknotes.some(item => !item._deleted)) {
    types.push('banknote');
  }
  if (form.ancientCoins.some(item => !item._deleted)) {
    types.push('ancientCoin');
  }
  if (form.machineCoins.some(item => !item._deleted)) {
    types.push('machineCoin');
  }
  if (form.silverIngots.some(item => !item._deleted)) {
    types.push('silverIngot');
  }

  // 如果没有任何钱币数据
  if (types.length === 0) {
    if (isUpdate.value) {
      // 编辑模式：显示所有类型
      return ['banknote', 'ancientCoin', 'machineCoin', 'silverIngot'];
    } else {
      // 新建模式：如果还没有添加过钱币，显示所有类型；如果已经添加过，不显示任何tab
      if (!hasAddedCoins.value) {
        return ['banknote', 'ancientCoin', 'machineCoin', 'silverIngot'];
      } else {
        return [];
      }
    }
  }

  return types;
});

/** 增加钱币对话框 */
const addCoinVisible = ref(false);

/** 本地添加钱币对话框 */
const localAddCoinVisible = ref(false);

/** 添加新钱币到表格 */
const addNewCoin = (coinType) => {
  // 生成临时ID
  const tempId = `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

  // 钱币类型映射
  const coinTypeMap = {
    'banknote': '01',
    'ancientCoin': '02',
    'machineCoin': '03',
    'silverIngot': '04'
  };

  // 生成钱币编号
  const coinNumber = '';

  // 创建新钱币对象
  const newCoin = {
    tempId: tempId,
    isTemp: true,
    coinType: coinType,
    // nummber: coinNumber,
    coinName1: '',
    coinName2: '',
    coinName3: '',
    yearInfo: '',
    faceValue: '',
    version: '',
    quantity: 1,
    gradeFee: 0,
    discount: 0,
    coinSize: '',
    coinWeight: 0,
    region: '',
    taxType: '',
    faceType: '',
    boxFee: 0,
    urgentFee: 0,
    catalog: '',
    rank: '',
    bankName: '',
    specialMark: '',
    internalNote: '',
    externalNote: '',
    specialLabel: '',
    authenticity: '真',
    gradeScore: '',
    scoreRemarks: '',
    inspectionNote: '',
    coinImages: null,
    belongName: '',
    weightName: '',
    material: '',
    boxType: '',
    standardPrice: 0,
    internationalPrice: 0,
    fee: 0,
    boxType: ''
  };

  // 根据钱币类型添加到对应数组
  switch (coinType) {
    case 'banknote':
      form.banknotes.push(newCoin);
      activeCoinType.value = 'banknote';
      break;
    case 'ancientCoin':
      form.ancientCoins.push(newCoin);
      activeCoinType.value = 'ancientCoin';
      break;
    case 'machineCoin':
      form.machineCoins.push(newCoin);
      activeCoinType.value = 'machineCoin';
      break;
    case 'silverIngot':
      form.silverIngots.push(newCoin);
      activeCoinType.value = 'silverIngot';
      break;
  }

  // 标记已经添加过钱币
  hasAddedCoins.value = true;

  // 滚动到新添加的钱币（延迟执行确保DOM更新完成）
  setTimeout(() => {
    if (layoutMode.value === 'card') {
      const cardContainers = document.querySelectorAll('.coin-cards');
      if (cardContainers.length > 0) {
        const activeContainer = cardContainers[cardContainers.length - 1]; // 获取当前激活的卡片容器
        activeContainer.scrollTop = activeContainer.scrollHeight; // 滚动到底部
      }
    } else {
      const tables = document.querySelectorAll('.el-table__body-wrapper');
      if (tables.length > 0) {
        const activeTable = tables[tables.length - 1]; // 获取当前激活的表格
        activeTable.scrollTop = activeTable.scrollHeight; // 滚动到底部
      }
    }
  }, 100);

  // EleMessage.success(`成功添加新${getCoinTypeName(coinType)}`);
};

/** 获取钱币类型中文名称 */
const getCoinTypeName = (coinType) => {
  const nameMap = {
    'banknote': '纸币',
    'ancientCoin': '古钱币',
    'machineCoin': '机制币',
    'silverIngot': '银锭'
  };
  return nameMap[coinType] || '钱币';
};

/** 增加钱币完成回调 */
const handleAddCoinDone = async () => {
  addCoinVisible.value = false;
  // 重新加载详情
  if (form.sendform.sendnum) {
    await loadSendformDetail(form.sendform.sendnum);
  }
};

/** 本地添加钱币回调 */
const handleLocalAddCoin = (coins) => {
  localAddCoinVisible.value = false;

  console.log('接收到的钱币数据:', coins);

  if (!coins || coins.length === 0) {
    console.log('没有钱币数据');
    return;
  }

  // 根据钱币类型分类添加
  const coinsByType = {
    'ancientCoin': [], // 古钱币
    'machineCoin': [], // 机制币
    'silverIngot': [], // 银锭
    'banknote': []  // 纸币
  };

  coins.forEach(coin => {
    console.log('处理钱币:', coin);
    const coinType = coin.coinType;
    console.log('钱币类型:', coinType);

    if (coinsByType[coinType]) {
      // 保持原有的唯一ID，不要重新生成
      const newCoin = {
        ...coin,
        isTemp: true // 确保临时钱币标记
      };
      coinsByType[coinType].push(newCoin);
      console.log('添加到类型数组:', coinType, newCoin);
    } else {
      console.log('未知的钱币类型:', coinType);
    }
  });

  // 分别添加到对应的数组
  console.log('钱币分类结果:', coinsByType);

  form.ancientCoins.push(...coinsByType['ancientCoin']);
  form.machineCoins.push(...coinsByType['machineCoin']);
  form.silverIngots.push(...coinsByType['silverIngot']);
  form.banknotes.push(...coinsByType['banknote']);

  // 标记已经添加过钱币
  hasAddedCoins.value = true;

  console.log('添加后的表单数据:', {
    ancientCoins: form.ancientCoins.length,
    machineCoins: form.machineCoins.length,
    silverIngots: form.silverIngots.length,
    banknotes: form.banknotes.length
  });

  // 切换到有数据的tab（纸币优先）
  if (coinsByType['banknote'].length > 0) activeCoinType.value = 'banknote';
  else if (coinsByType['ancientCoin'].length > 0) activeCoinType.value = 'ancientCoin';
  else if (coinsByType['machineCoin'].length > 0) activeCoinType.value = 'machineCoin';
  else if (coinsByType['silverIngot'].length > 0) activeCoinType.value = 'silverIngot';

  console.log('切换到tab:', activeCoinType.value);

  // 确保激活的tab在可见类型列表中
  setTimeout(() => {
    if (!visibleCoinTypes.value.includes(activeCoinType.value)) {
      if (visibleCoinTypes.value.length > 0) {
        activeCoinType.value = visibleCoinTypes.value[0];
      }
    }
  }, 100);

  const totalAdded = coins.length;
  // EleMessage.success(`成功添加 ${totalAdded} 条钱币记录`);
};

/** 加载送评单详情 */
const loadSendformDetail = async (sendnum) => {
  try {
    const detail = await getSendformDetail(sendnum);
    assignFields({
      sendform: detail.sendform || {},
      ancientCoins: detail.ancientCoins || [],
      machineCoins: detail.machineCoins || [],
      silverIngots: detail.silverIngots || [],
      banknotes: detail.banknotes || []
    });
  } catch (error) {
    EleMessage.error('加载送评单详情失败');
  }
};

/** 处理钱币字段变化 */
const onCoinFieldChange = ({ row, field }) => {
  // 实时保存字段变化
  console.log('钱币字段变化:', { row, field });
};

/** 删除钱币 */
const onCoinDelete = ({ row, index }) => {
  // 根据当前tab确定是从哪个数组删除
  let targetArray = null;

  switch (activeCoinType.value) {
    case 'ancientCoin':
      targetArray = form.ancientCoins;
      break;
    case 'machineCoin':
      targetArray = form.machineCoins;
      break;
    case 'silverIngot':
      targetArray = form.silverIngots;
      break;
    case 'banknote':
      targetArray = form.banknotes;
      break;
  }

  if (targetArray) {
    // 在目标数组中找到要删除的项目
    const targetIndex = targetArray.findIndex(item => {
      // 根据唯一标识来匹配项目
      if (row.id && item.id) {
        return row.id === item.id;
      }
      if (row.tempId && item.tempId) {
        return row.tempId === item.tempId;
      }
      if (row.nummber && item.nummber) {
        return row.nummber === item.nummber;
      }
      // 如果没有唯一标识，则比较对象引用
      return item === row;
    });

    if (targetIndex >= 0) {
      if (row.isTemp) {
        // 删除本地临时钱币
        targetArray.splice(targetIndex, 1);
      } else {
        // 已保存的钱币，标记删除
        targetArray[targetIndex]._deleted = true;
      }

      // 删除后检查当前tab是否还有数据，如果没有则切换到有数据的tab
      setTimeout(() => {
        if (!visibleCoinTypes.value.includes(activeCoinType.value)) {
          if (visibleCoinTypes.value.length > 0) {
            activeCoinType.value = visibleCoinTypes.value[0];
          }
        }
      }, 100);
    }
  }
};

/** 提交 */
const onSubmit = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }

    loading.value = true;

    // 检查是否至少添加了一种钱币（新增时）
    if (!isUpdate.value) {
      const hasCoins = form.ancientCoins.length > 0 ||
                      form.machineCoins.length > 0 ||
                      form.silverIngots.length > 0 ||
                      form.banknotes.length > 0;

      if (!hasCoins) {
        loading.value = false;
        EleMessage.warning('请至少添加一种钱币');
        return;
      }
    }

    // 清理临时字段，准备提交
    const cleanCoinData = (item) => {
      const { isTemp, id, tempId, ...rest } = item;
      // 只有当id是数字类型时才保留，否则设为undefined让后端生成
      const cleanItem = { ...rest };
      if (typeof id === 'number') {
        cleanItem.id = id;
      }
      return cleanItem;
    };

    const submitData = {
      sendform: form.sendform,
      ancientCoins: form.ancientCoins
        .filter(item => !item._deleted)
        .map(cleanCoinData),
      machineCoins: form.machineCoins
        .filter(item => !item._deleted)
        .map(cleanCoinData),
      silverIngots: form.silverIngots
        .filter(item => !item._deleted)
        .map(cleanCoinData),
      banknotes: form.banknotes
        .filter(item => !item._deleted)
        .map(cleanCoinData)
    };

    console.log('提交数据:', submitData);

    // 统一使用 saveOrUpdate 接口
    saveOrUpdateSendform(submitData).then((result) => {
      loading.value = false;
      const message = isUpdate.value ? '更新送评单成功' : '创建送评单成功';
      EleMessage.success(message);
      updateModelValue(false);
      emit('done');
    }).catch((e) => {
      loading.value = false;
      EleMessage.error(e.message);
    });
  });
};

/** 更新modelValue */
const updateModelValue = (value) => {
  emit('update:modelValue', value);
};

watch(
  () => props.modelValue,
  async (modelValue) => {
    if (modelValue) {
      if (props.data) {
        // 编辑模式：加载详情
        if (props.data.sendnum) {
          await loadSendformDetail(props.data.sendnum);
        } else {
          assignFields({
            sendform: { ...props.data },
            ancientCoins: [],
            machineCoins: [],
            silverIngots: [],
            banknotes: []
          });
        }

        // 编辑模式下，确保激活的tab在可见范围内
        setTimeout(() => {
          if (!visibleCoinTypes.value.includes(activeCoinType.value)) {
            if (visibleCoinTypes.value.length > 0) {
              activeCoinType.value = visibleCoinTypes.value[0];
            }
          }
        }, 100);
      } else {
        // 新建模式：重置表单并设置默认值
        resetFields();
        // 自动设置当前用户的网名
        form.sendform.nickname = getCurrentUserNickname();
        // 新建模式默认激活纸币tab
        activeCoinType.value = 'banknote';
        // 重置添加钱币状态
        hasAddedCoins.value = false;
      }
    } else {
      resetFields();
      formRef.value?.clearValidate?.();
      // 重置添加钱币状态
      hasAddedCoins.value = false;
    }
  }
);
</script>

<style scoped>
.section-title {
  margin: 20px 0 16px 0;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
}

.section-title h4 {
  margin: 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.title-with-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.layout-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

:deep(.el-tabs__header) {
  margin: 0 0 16px 0;
}

:deep(.el-tabs__content) {
  padding: 0;
}

.empty-hint {
  text-align: center;
  color: #909399;
  font-size: 14px;
  padding: 40px 0;
  border: 1px dashed #dcdfe6;
  border-radius: 4px;
  margin-top: 16px;
}
</style>
