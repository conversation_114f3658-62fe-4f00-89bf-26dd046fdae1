<template>
  <div class="coin-detail-card">
    <div v-if="computedTableData.length === 0" class="empty-state">
      <el-empty description="暂无钱币数据" />
    </div>

    <div v-else>
      <!-- 批量操作工具栏 -->
      <div v-if="computedTableData.length > 1" class="card-toolbar">
        <div class="toolbar-actions">
          <el-button
            type="primary"
            size="small"
            text
            @click="expandAll"
          >
            展开全部
          </el-button>
          <el-button
            type="primary"
            size="small"
            text
            @click="collapseAll"
          >
            收起全部
          </el-button>
        </div>
      </div>

      <div class="coin-cards">
      <div
        v-for="(coin, index) in computedTableData"
        :key="coin.tempId || coin.id || index"
        class="coin-card"
        :class="{ 'new-coin-card': coin.isTemp }"
      >
        <!-- 卡片头部 -->
        <div class="card-header">
          <div class="coin-number">
            <span class="number-label">编号：</span>
            <span class="number-value">{{ coin.serialNumber }}</span>
          </div>
          <div class="card-actions">
            <el-button
              type="primary"
              size="small"
              text
              @click="toggleExpand(coin.tempId || coin.id || index)"
            >
              {{ expandedCards.includes(coin.tempId || coin.id || index) ? '收起' : '展开' }}
            </el-button>
            <el-button
              type="danger"
              size="small"
              text
              @click="handleDelete(coin, index)"
            >
              删除
            </el-button>
          </div>
        </div>

        <!-- 基本信息概览 -->
        <div class="card-summary">
          <div class="summary-row">
            <div class="summary-item">
              <span class="label">名称：</span>
              <el-input
                v-model="coin.coinName1"
                placeholder="钱币名称"
                size="small"
                @change="handleFieldChange(coin, 'coinName1')"
              />
            </div>
            <div class="summary-item">
              <span class="label">年代：</span>
              <el-input
                v-model="coin.yearInfo"
                placeholder="年代"
                size="small"
                @change="handleFieldChange(coin, 'yearInfo')"
              />
            </div>
            <div class="summary-item">
              <span class="label">数量：</span>
              <el-input-number
                v-model="coin.quantity"
                :min="1"
                size="small"
                @change="handleFieldChange(coin, 'quantity')"
              />
            </div>
          </div>
        </div>

        <!-- 详细信息（展开时显示） -->
        <el-collapse-transition>
          <div v-if="expandedCards.includes(coin.tempId || coin.id || index)" class="card-details">

            <!-- 基本信息组 -->
            <div class="detail-group">
              <div class="group-title">基本信息</div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="label">名称2：</span>
                  <el-input
                    v-model="coin.coinName2"
                    placeholder="钱币名称2"
                    size="small"
                    @change="handleFieldChange(coin, 'coinName2')"
                  />
                </div>
<!--                <div class="detail-item">
                  <span class="label">名称3：</span>
                  <el-input
                    v-model="coin.coinName3"
                    placeholder="钱币名称3"
                    size="small"
                    @change="handleFieldChange(coin, 'coinName3')"
                  />
                </div>-->
                <div class="detail-item">
                  <span class="label">版别：</span>
                  <dict-data
                    v-model="coin.version"
                    code="edition"
                    type="select"
                    placeholder="请选择版别"
                    filterable
                    size="small"
                    @change="handleFieldChange(coin, 'version')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">盒子类型：</span>
                  <dict-data
                    v-model="coin.boxType"
                    code="boxType"
                    type="select"
                    placeholder="盒子类型"
                    size="small"
                    @change="handleFieldChange(coin, 'boxType')"
                  />
                </div>
              </div>
            </div>

            <!-- 钱币类型特定字段 -->
            <div class="detail-group">
              <div class="group-title">{{ getCoinTypeName(coinType) }}专用信息</div>
              <div class="detail-row">

                <!-- 纸币专用字段 -->
                <template v-if="isBanknote">
                  <div class="detail-item">
                    <span class="label">目录：</span>
                    <el-input
                      v-model="coin.catalog"
                      placeholder="目录"
                      size="small"
                      @change="handleFieldChange(coin, 'catalog')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">银行名称：</span>
                    <el-input
                      v-model="coin.bankName"
                      placeholder="银行名称"
                      size="small"
                      @change="handleFieldChange(coin, 'bankName')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">特殊标签：</span>
                    <el-input
                      v-model="coin.specialLabel"
                      placeholder="特殊标签"
                      size="small"
                      @change="handleFieldChange(coin, 'specialLabel')"
                    />
                  </div>
                </template>

                <!-- 古钱币专用字段 -->
                <template v-if="isAncientCoin">
                  <div class="detail-item">
                    <span class="label">材质：</span>
                    <dict-data
                      v-model="coin.material"
                      code="material_ancient"
                      type="select"
                      placeholder="材质"
                      size="small"
                      @change="handleFieldChange(coin, 'material')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">等级：</span>
                    <el-input
                      v-model="coin.rank"
                      placeholder="等级"
                      size="small"
                      @change="handleFieldChange(coin, 'rank')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">对内备注：</span>
                    <el-input
                      v-model="coin.internalNote"
                      placeholder="对内备注"
                      size="small"
                      @change="handleFieldChange(coin, 'internalNote')"
                    />
                  </div>
                </template>

                <!-- 机制币专用字段 -->
                <template v-if="isMachineCoin">
                  <div class="detail-item">
                    <span class="label">面值：</span>
                    <el-input
                      v-model="coin.faceValue"
                      placeholder="面值"
                      size="small"
                      @change="handleFieldChange(coin, 'faceValue')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">材质：</span>
                    <dict-data
                      v-model="coin.material"
                      code="material_machine"
                      type="select"
                      placeholder="材质"
                      size="small"
                      @change="handleFieldChange(coin, 'material')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">尺寸：</span>
                    <el-input
                      v-model="coin.coinSize"
                      placeholder="尺寸"
                      size="small"
                      @change="handleFieldChange(coin, 'coinSize')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">重量：</span>
                    <el-input
                      v-model="coin.coinWeight"
                      placeholder="重量"
                      size="small"
                      @change="handleFieldChange(coin, 'coinWeight')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">对内备注：</span>
                    <el-input
                      v-model="coin.internalNote"
                      placeholder="对内备注"
                      size="small"
                      @change="handleFieldChange(coin, 'internalNote')"
                    />
                  </div>
                </template>

                <!-- 银锭专用字段 -->
                <template v-if="isSilverIngot">
                  <div class="detail-item">
                    <span class="label">年份：</span>
                    <el-input
                      v-model="coin.year"
                      placeholder="年份"
                      size="small"
                      @change="handleFieldChange(coin, 'year')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">地区：</span>
                    <el-input
                      v-model="coin.region"
                      placeholder="地区"
                      size="small"
                      @change="handleFieldChange(coin, 'region')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">税种：</span>
                    <el-input
                      v-model="coin.taxType"
                      placeholder="税种"
                      size="small"
                      @change="handleFieldChange(coin, 'taxType')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">面值：</span>
                    <el-input
                      v-model="coin.faceValue"
                      placeholder="面值"
                      size="small"
                      @change="handleFieldChange(coin, 'faceValue')"
                    />
                  </div>
                  <div class="detail-item">
                    <span class="label">材质：</span>
                    <dict-data
                      v-model="coin.material"
                      code="material_silver"
                      type="select"
                      placeholder="材质"
                      size="small"
                      @change="handleFieldChange(coin, 'material')"
                    />
                  </div>
                </template>
              </div>
            </div>

            <!-- 价格信息组 -->
            <div class="detail-group">
              <div class="group-title">价格信息</div>
              <div class="detail-row">
                <div class="detail-item">
                  <span class="label">标准价：</span>
                  <el-input-number
                    v-model="coin.standardPrice"
                    :min="0"
                    :precision="2"
                    size="small"
                    @change="handleFieldChange(coin, 'standardPrice')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">国际价：</span>
                  <el-input-number
                    v-model="coin.internationalPrice"
                    :min="0"
                    :precision="2"
                    size="small"
                    @change="handleFieldChange(coin, 'internationalPrice')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">费用：</span>
                  <el-input-number
                    v-model="coin.gradeFee"
                    :min="0"
                    :precision="2"
                    size="small"
                    @change="handleFieldChange(coin, 'gradeFee')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">折扣：</span>
                  <el-input-number
                    v-model="coin.discount"
                    :min="0"
                    :max="100"
                    :precision="1"
                    size="small"
                    @change="handleFieldChange(coin, 'discount')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">盒子费：</span>
                  <el-input-number
                    v-model="coin.boxFee"
                    :min="0"
                    :precision="2"
                    size="small"
                    @change="handleFieldChange(coin, 'boxFee')"
                  />
                </div>
                <div class="detail-item">
                  <span class="label">加急费：</span>
                  <el-input-number
                    v-model="coin.urgentFee"
                    :min="0"
                    :precision="2"
                    size="small"
                    @change="handleFieldChange(coin, 'urgentFee')"
                  />
                </div>
              </div>
            </div>

            <!-- 图片信息组 -->
            <div class="detail-group">
              <div class="group-title">图片信息</div>
              <div class="detail-row">
                <div class="detail-item full-width">
                  <coin-image-upload
                    v-model="coin.coinImages"
                    :limit="3"
                    :show-title="false"
                    :show-info="false"
                    :item-style="{ width: '80px', height: '80px' }"
                    :button-style="{ width: '80px', height: '80px' }"
                    placeholder="上传图片"
                    :max-size="5"
                    @upload-success="(data) => handleImageChange(coin, data, 'upload')"
                    @remove-success="(data) => handleImageChange(coin, data, 'remove')"
                  />
                </div>
              </div>
            </div>

          </div>
        </el-collapse-transition>
      </div>
    </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import DictData from '@/components/DictData/index.vue'
import CoinImageUpload from '@/components/CoinImageUpload/index.vue'
import { useCoinTypes } from '@/composables/use-coin-types'


export default defineComponent({
  name: 'CoinDetailCard',
  components: {
    DictData,
    CoinImageUpload
  },
  props: {
    tableData: {
      type: Array,
      default: () => []
    },
    coins: {
      type: Array,
      default: () => []
    },
    coinType: {
      type: String,
      default: 'banknote' // 验证器已改为动态获取
    }
  },
  emits: ['update', 'delete', 'field-change'],
  setup(props, { emit }) {
    // 展开的卡片
    const expandedCards = ref([])

    // 使用动态代码表替代硬编码判断
    const {
      checkIsBanknote,
      checkIsAncientCoin,
      checkIsMachineCoin,
      checkIsSilverIngot
    } = useCoinTypes()



    // 动态类型判断计算属性
    const isBanknote = computed(() => checkIsBanknote(props.coinType))
    const isAncientCoin = computed(() => checkIsAncientCoin(props.coinType))
    const isMachineCoin = computed(() => checkIsMachineCoin(props.coinType))
    const isSilverIngot = computed(() => checkIsSilverIngot(props.coinType))

    // 表格数据
    const computedTableData = computed(() => {
      const data = props.tableData || props.coins || []
      return data.filter(item => !item._deleted)
    })

    // 监听数据变化，自动展开新添加的卡片
    watch(() => computedTableData.value, (newData, oldData) => {
      if (newData.length > (oldData?.length || 0)) {
        // 有新数据添加，找到新添加的项目并展开
        const newItems = newData.filter(item => {
          const itemId = item.tempId || item.id
          return itemId && !expandedCards.value.includes(itemId)
        })

        newItems.forEach(item => {
          const itemId = item.tempId || item.id
          if (itemId && !expandedCards.value.includes(itemId)) {
            expandedCards.value.push(itemId)
          }
        })
      }
    }, { deep: true })

    // 获取钱币类型名称
    const getCoinTypeName = (coinType) => {
      const nameMap = {
        'banknote': '纸币',
        'ancientCoin': '古钱币',
        'machineCoin': '机制币',
        'silverIngot': '银锭'
      }
      return nameMap[coinType] || '钱币'
    }

    // 切换展开/收起
    const toggleExpand = (cardId) => {
      const index = expandedCards.value.indexOf(cardId)
      if (index > -1) {
        expandedCards.value.splice(index, 1)
      } else {
        expandedCards.value.push(cardId)
      }
    }

    // 处理字段变化
    const handleFieldChange = (row, field) => {
      emit('field-change', { row, field })
    }

    // 删除钱币
    const handleDelete = async (row, index) => {
      try {
        await ElMessageBox.confirm(
          `确定要删除钱币编号为 "${row.nummber}" 的记录吗？`,
          '删除确认',
          {
            type: 'warning'
          }
        )

        emit('delete', { row, index })
        ElMessage.success('删除成功')
      } catch (error) {
        if (error !== 'cancel') {
          ElMessage.error('删除失败')
        }
      }
    }

    // 处理图片变更
    const handleImageChange = (row, data, action) => {
      console.log(`图片${action}:`, data)
      handleFieldChange(row, 'coinImages')
    }

    // 展开全部
    const expandAll = () => {
      expandedCards.value = computedTableData.value.map(item => item.tempId || item.id || computedTableData.value.indexOf(item))
    }

    // 收起全部
    const collapseAll = () => {
      expandedCards.value = []
    }

    // 组件挂载后，默认展开所有卡片
    onMounted(() => {
      // 延迟一点执行，确保数据已经加载
      setTimeout(() => {
        expandAll()
      }, 100)
    })

    return {
      expandedCards,
      computedTableData,
      getCoinTypeName,
      toggleExpand,
      handleFieldChange,
      handleDelete,
      handleImageChange,
      expandAll,
      collapseAll
    }
  }
})
</script>

<style scoped>
.coin-detail-card {
  padding: 8px;
}

.empty-state {
  text-align: center;
  padding: 60px 0;
}

.card-toolbar {
  padding: 12px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 16px;
}

.toolbar-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.coin-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.coin-card {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  background: #fff;
  transition: all 0.3s;
}

.coin-card:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.new-coin-card {
  border-color: #409eff;
  background: #f0f9ff;
  animation: highlight-new-card 2s ease-in-out;
}

@keyframes highlight-new-card {
  0% {
    transform: scale(1.02);
    box-shadow: 0 4px 16px rgba(64, 158, 255, 0.3);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px;
  border-bottom: 1px solid #ebeef5;
}

.coin-number {
  display: flex;
  align-items: center;
  font-weight: 500;
}

.number-label {
  color: #606266;
  margin-right: 8px;
}

.number-value {
  font-family: monospace;
  font-weight: bold;
  color: #409eff;
  font-size: 14px;
}

.card-actions {
  display: flex;
  gap: 8px;
}

.card-summary {
  padding: 16px;
}

.summary-row {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.summary-item {
  flex: 1;
  min-width: 200px;
  display: flex;
  align-items: center;
  gap: 8px;
}

.summary-item .label {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  min-width: 60px;
}

.card-details {
  border-top: 1px solid #ebeef5;
  background: #fafafa;
}

.detail-group {
  padding: 16px;
}

.detail-group:not(:last-child) {
  border-bottom: 1px solid #ebeef5;
}

.group-title {
  font-weight: 500;
  color: #303133;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid #e4e7ed;
}

.detail-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.detail-item.full-width {
  grid-column: 1 / -1;
}

.detail-item .label {
  color: #606266;
  font-size: 14px;
  white-space: nowrap;
  min-width: 80px;
}

.detail-item :deep(.el-input),
.detail-item :deep(.el-input-number),
.detail-item :deep(.el-select) {
  flex: 1;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .summary-row {
    flex-direction: column;
  }

  .summary-item {
    min-width: auto;
  }

  .detail-row {
    grid-template-columns: 1fr;
  }
}
</style>
