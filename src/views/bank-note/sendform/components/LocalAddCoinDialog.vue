<template>
  <el-dialog
    v-model="visible"
    title="添加钱币"
    width="900px"
    :destroy-on-close="true"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="120px" size="small">
      <!-- 钱币类型选择 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="钱币类型" prop="coinType" required>
            <el-input
              v-model="coinTypeDisplay"
              disabled
              placeholder="钱币类型"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="钱币名称" prop="coinName" required>
            <el-input v-model="form.coinName" placeholder="请输入钱币名称" />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 基本信息 -->
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="年代">
            <el-input v-model="form.year" placeholder="请输入年代" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="面值">
            <el-input v-model="form.faceValue" placeholder="请输入面值" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="版别">
            <el-input v-model="form.version" placeholder="请输入版别" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="数量">
            <el-input-number
              v-model="form.quantity"
              :min="1"
              placeholder="请输入数量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="评级费">
            <el-input-number
              v-model="form.gradeFee"
              :precision="2"
              placeholder="请输入评级费"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量">
            <el-input-number
              v-model="form.weight"
              :precision="3"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <!-- 扩展信息 -->
      <el-row :gutter="16">
        <el-col :span="12">
          <el-form-item label="所属名称">
            <el-input v-model="form.belongName" placeholder="请输入所属名称" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="材质">
            <el-input v-model="form.material" placeholder="请输入材质" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="24">
          <el-form-item label="备注">
            <el-input
              v-model="form.remark"
              type="textarea"
              :rows="3"
              placeholder="请输入备注"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>

    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleConfirm" :loading="loading">
        确定添加
      </el-button>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useFormData } from '@/utils/use-form-data';
import { EleMessage } from 'ele-admin-plus';
import { useCoinTypes } from '@/composables/use-coin-types';

const emit = defineEmits(['update:modelValue', 'confirm']);

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  defaultCoinType: {
    type: String,
    default: 'banknote'
  }
});

/** 表单实例 */
const formRef = ref(null);

/** 表单数据 */
const [form, resetFields] = useFormData({
  coinType: 'banknote',
  coinName: '',
  year: '',
  faceValue: '',
  version: '',
  quantity: 1,
  gradeFee: null,
  weight: null,
  belongName: '',
  material: '',
  remark: ''
});

/** 表单验证规则 */
const rules = {
  coinType: [
    { required: true, message: '请选择钱币类型', trigger: 'change' }
  ],
  coinName: [
    { required: true, message: '请输入钱币名称', trigger: 'blur' }
  ]
};

/** 提交状态 */
const loading = ref(false);

/** 对话框显示状态 */
const visible = ref(false);

/** 使用动态代码表替代硬编码映射 */
const { coinTypeMap, getCoinTypeName } = useCoinTypes();

/** 钱币类型中文显示 */
const coinTypeDisplay = computed(() => {
  return getCoinTypeName(form.coinType);
});

/** 关闭对话框 */
const handleClose = () => {
  emit('update:modelValue', false);
  resetFields();
  formRef.value?.clearValidate?.();
};

/** 确定添加 */
const handleConfirm = () => {
  formRef.value?.validate?.((valid) => {
    if (!valid) {
      return;
    }

    loading.value = true;

    try {
      // 根据数量生成多条记录
      const quantity = form.quantity || 1;

      // 批量生成钱币编号
      const coinNumbers = '';

      const coinList = [];

      for (let i = 0; i < quantity; i++) {
        const tempId = `temp_${Date.now()}_${Math.random()}`;
        const coinData = {
          id: tempId,
          tempId: tempId,
          isTemp: true, // 标记为临时数据
          coinType: form.coinType,
          sendnum: '', // 创建时为空
          serialNumber: i + 1, // 序列号
          // nummber: coinNumbers[i],
          // 基本信息（使用原系统字段名）
          coinName1: form.coinName, // 名称1
          coinName2: '', // 名称2
          coinName3: '', // 名称3
          yearInfo: form.year, // 年代
          faceValue: form.faceValue, // 面值
          version: form.version, // 版别
          gradeFee: form.gradeFee || 0, // 评级费/费用
          remark: form.remark,

          // 扩展信息
          belongName: form.belongName,
          material: form.material,
          coinWeight: form.weight,
          coinSize: '', // 尺寸

          // 表格需要的字段
          boxNumber: '', // 盒子编号
          amount: 1, // 数量
          standardPrice: 0, // 标准价
          internationalPrice: 0, // 国际价
          discount: 0, // 折扣
          boxType: 1, // 盒子类型（默认密封盒）
          externalNote: '', // 对外备注

          // 纸币专用字段
          catalog: '', // 目录
          bank: '', // 银行名称
          catalogType: '', // 样册分类
          company: 1, // 内置公司（默认中乾评级）

          // 银锭专用字段
          reign: '', // 年号
          region: '', // 地区
          taxType: '', // 锻件（税种）

          // 古钱币专用字段
          grade: '', // 自然（等级）

          // 其他必要字段
          authenticity: '',
          gradeScore: null,
          verificationNote: ''
        };

        coinList.push(coinData);
      }

      setTimeout(() => {
        loading.value = false;
        // 一次性返回所有钱币数组
        emit('confirm', coinList);
        EleMessage.success(`成功添加 ${coinList.length} 条钱币记录`);
        handleClose();
      }, 300);
    } catch (error) {
      loading.value = false;
      console.error('添加钱币失败:', error);
      EleMessage.error('添加钱币失败，请重试');
    }
  });
};

watch(() => props.modelValue, (val) => {
  visible.value = val;
  if (val) {
    // 对话框打开时根据当前选中的tab设置默认钱币类型
    setTimeout(() => {
      form.coinType = props.defaultCoinType || 'banknote';
      form.quantity = 1;
    }, 100);
  }
});

watch(visible, (val) => {
  if (!val) {
    emit('update:modelValue', false);
  }
});
</script>
