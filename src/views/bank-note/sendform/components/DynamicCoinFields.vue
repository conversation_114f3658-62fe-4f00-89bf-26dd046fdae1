<template>
  <div class="dynamic-coin-fields">
    <!-- 纸币专用字段 -->
    <template v-if="isBanknote">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="面值">
            <el-input v-model="form.faceVal" placeholder="请输入面值" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="银行名称">
            <el-input v-model="form.bankName" placeholder="请输入银行名称" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="编号">
            <el-input v-model="form.serialNumber" placeholder="请输入编号" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="地区">
            <el-input v-model="form.region" placeholder="请输入地区" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="尺寸">
            <el-input v-model="form.coinSize" placeholder="请输入尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量">
            <el-input-number
              v-model="form.coinWeight"
              :precision="3"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 古钱币专用字段 -->
    <template v-if="isAncientCoin">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="材质">
            <dict-data
              v-model="form.material"
              code="material_ancient"
              type="select"
              placeholder="请选择材质"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量">
            <el-input-number
              v-model="form.coinWeight"
              :precision="3"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="尺寸">
            <el-input v-model="form.coinSize" placeholder="请输入尺寸" />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="地区">
            <el-input v-model="form.region" placeholder="请输入地区" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="目录">
            <el-input v-model="form.catalog" placeholder="请输入目录" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="等级">
            <el-input v-model="form.rank" placeholder="请输入等级" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 机制币专用字段 -->
    <template v-if="isMachineCoin">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="面值">
            <el-input v-model="form.faceVal" placeholder="请输入面值" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="材质">
            <dict-data
              v-model="form.material"
              code="material_machine"
              type="select"
              placeholder="请选择材质"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量">
            <el-input-number
              v-model="form.coinWeight"
              :precision="3"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="尺寸">
            <el-input v-model="form.coinSize" placeholder="请输入尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地区">
            <el-input v-model="form.region" placeholder="请输入地区" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="目录">
            <el-input v-model="form.catalog" placeholder="请输入目录" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 银锭专用字段 -->
    <template v-if="isSilverIngot">
      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="面值">
            <el-input v-model="form.faceVal" placeholder="请输入面值" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="材质">
            <dict-data
              v-model="form.material"
              code="material_silver"
              type="select"
              placeholder="请选择材质"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="重量">
            <el-input-number
              v-model="form.coinWeight"
              :precision="3"
              placeholder="请输入重量"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
      </el-row>

      <el-row :gutter="16">
        <el-col :span="8">
          <el-form-item label="尺寸">
            <el-input v-model="form.coinSize" placeholder="请输入尺寸" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="地区">
            <el-input v-model="form.region" placeholder="请输入地区" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="等级">
            <el-input v-model="form.rank" placeholder="请输入等级" />
          </el-form-item>
        </el-col>
      </el-row>
    </template>

    <!-- 通用字段 -->
    <el-row :gutter="16">
      <el-col :span="8">
        <el-form-item label="盒子类型">
          <dict-data
            v-model="form.boxType"
            code="packType"
            type="select"
            placeholder="请选择盒子类型"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="数量">
          <el-input-number
            v-model="form.quantity"
            :min="1"
            placeholder="请输入数量"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
      <el-col :span="8">
        <el-form-item label="费用">
          <el-input-number
            v-model="form.gradeFee"
            :precision="2"
            placeholder="请输入费用"
            style="width: 100%"
          />
        </el-form-item>
      </el-col>
    </el-row>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import DictData from '@/components/DictData/index.vue';
import { useCoinTypeConditions } from '@/composables/use-coin-types';

const props = defineProps({
  coinType: {
    type: String,
    default: ''
  },
  form: {
    type: Object,
    required: true
  }
});

// 使用动态代码表替代硬编码类型判断
const conditions = useCoinTypeConditions(computed(() => props.coinType));

// 响应式的类型判断
const isBanknote = computed(() => conditions.value.isBanknote);
const isAncientCoin = computed(() => conditions.value.isAncientCoin);
const isMachineCoin = computed(() => conditions.value.isMachineCoin);
const isSilverIngot = computed(() => conditions.value.isSilverIngot);
</script>

<style scoped>
.dynamic-coin-fields {
  margin-top: 16px;
}
</style>
