<template>
  <div class="upload-debug">
    <h3>图片上传调试工具</h3>

    <el-card style="margin-bottom: 20px;">
      <template #header>
        <span>上传测试</span>
      </template>

      <el-upload
        action=""
        :before-upload="handleUpload"
        :show-file-list="false"
        accept="image/*"
      >
        <el-button type="primary">选择图片上传</el-button>
      </el-upload>

      <div v-if="uploadResult" style="margin-top: 20px;">
        <h4>上传结果：</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(uploadResult, null, 2) }}</pre>

        <div v-if="imageUrl" style="margin-top: 10px;">
          <h4>测试不同的图片URL：</h4>
          <div v-for="(url, index) in testUrls" :key="index" style="margin-bottom: 15px; border: 1px solid #eee; padding: 10px;">
            <p><strong>URL {{ index + 1 }}:</strong> {{ url }}</p>
            <img :src="url" style="max-width: 150px; max-height: 150px; margin-right: 10px;"
                 @load="() => onImageLoad(index + 1)"
                 @error="() => onImageError(index + 1)" />
          </div>
        </div>
      </div>
    </el-card>

    <el-card>
      <template #header>
        <span>API测试</span>
      </template>

      <el-button @click="testFileAccess" type="success">测试文件访问API</el-button>
      <el-button @click="openInNewTab" type="info" style="margin-left: 10px;">在新标签页中打开图片</el-button>

      <div v-if="apiTestResult" style="margin-top: 20px;">
        <h4>API测试结果：</h4>
        <pre style="background: #f5f5f5; padding: 10px; border-radius: 4px;">{{ JSON.stringify(apiTestResult, null, 2) }}</pre>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { EleMessage } from 'ele-admin-plus';
import { uploadFile, getInlineFile } from '@/api/system/file';

const uploadResult = ref(null);
const imageUrl = ref('');
const apiTestResult = ref(null);
const testUrls = ref([]);

const handleUpload = async (file) => {
  try {
    EleMessage.loading('上传中...');

    console.log('开始上传文件:', file);
    const result = await uploadFile(file);

    console.log('上传原始结果:', result);
    uploadResult.value = result;

    // 解析返回的数据
    let fileData;
    if (typeof result === 'string') {
      try {
        const parsed = JSON.parse(result);
        fileData = Array.isArray(parsed) ? parsed[0] : parsed;
      } catch (e) {
        console.error('解析失败:', result);
        EleMessage.error('上传返回数据格式错误');
        return false;
      }
    } else if (Array.isArray(result)) {
      fileData = result[0];
    } else {
      fileData = result;
    }

    console.log('解析后的文件数据:', fileData);

    if (fileData && fileData.id) {
      // 生成多个可能的URL进行测试
      testUrls.value = [
        `/api/file/inline/${fileData.id}`,
        `/file/inline/${fileData.id}`,
        `/api/file/${fileData.id}`,
        `/file/${fileData.id}`,
        `http://localhost:8080/api/file/inline/${fileData.id}`,
        `http://localhost:3000/api/file/inline/${fileData.id}`
      ];

      imageUrl.value = testUrls.value[0];
      console.log('生成的测试URLs:', testUrls.value);
    }

    EleMessage.success('上传成功');

  } catch (error) {
    console.error('上传失败:', error);
    EleMessage.error('上传失败: ' + error.message);
  }

  return false; // 阻止默认上传行为
};

const testFileAccess = async () => {
  if (!uploadResult.value) {
    EleMessage.warning('请先上传一个文件');
    return;
  }

  try {
    let fileData;
    if (typeof uploadResult.value === 'string') {
      const parsed = JSON.parse(uploadResult.value);
      fileData = Array.isArray(parsed) ? parsed[0] : parsed;
    } else if (Array.isArray(uploadResult.value)) {
      fileData = uploadResult.value[0];
    } else {
      fileData = uploadResult.value;
    }

    if (fileData && fileData.id) {
      console.log('测试文件访问API, ID:', fileData.id);
      const result = await getInlineFile(fileData.id);
      apiTestResult.value = result;
      console.log('文件访问API结果:', result);
      EleMessage.success('API测试成功');
    } else {
      EleMessage.error('没有找到文件ID');
    }
  } catch (error) {
    console.error('API测试失败:', error);
    apiTestResult.value = { error: error.message };
    EleMessage.error('API测试失败: ' + error.message);
  }
};

const onImageLoad = (urlIndex) => {
  console.log(`图片加载成功 - URL ${urlIndex}:`, testUrls.value[urlIndex - 1]);
  EleMessage.success(`URL ${urlIndex} 图片显示成功`);
};

const onImageError = (urlIndex) => {
  console.log(`图片加载失败 - URL ${urlIndex}:`, testUrls.value[urlIndex - 1]);
};

const openInNewTab = () => {
  if (testUrls.value.length > 0) {
    testUrls.value.forEach((url, index) => {
      console.log(`打开URL ${index + 1}:`, url);
      window.open(url, `_blank_${index}`);
    });
  } else {
    EleMessage.warning('请先上传一个文件');
  }
};
</script>

<style scoped>
.upload-debug {
  padding: 20px;
  max-width: 800px;
  margin: 0 auto;
}

pre {
  max-height: 300px;
  overflow-y: auto;
  font-size: 12px;
}

img {
  border: 1px solid #ddd;
  border-radius: 4px;
  margin-top: 10px;
}
</style>
