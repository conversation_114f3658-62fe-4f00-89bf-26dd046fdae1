<template>
  <ele-page flex-table>
    <search @search="reload" />
    <ele-card
      flex-table
      :body-style="{ padding: '0 8px 10px 8px!important', overflow: 'hidden' }"
    >
      <!-- 数据表格 -->
      <ele-pro-table
        ref="tableRef"
        row-key="id"
        :columns="columns"
        :datasource="datasource"
        :border="true"
        :show-overflow-tooltip="true"
        v-model:selections="selections"
        highlight-current-row
        tooltip-effect="light"
        cache-key="bankNoteSendformTable"
        :footer-style="{ paddingBottom: '3px' }"
        style="padding-bottom: 0"
      >
        <template #toolbar>
          <el-button
            v-if="hasPermission('banknote:sendform:saveOrUpdate')"
            size="small"
            class="ele-btn-icon"
            @click="openEdit()"
          >
            新建
          </el-button>
          <el-button
            v-if="hasPermission('banknote:sendform:addCoin')"
            size="small"
            class="ele-btn-icon"
            @click="addCoin"
            :disabled="!selections.length || selections.length > 1"
          >
            增加钱币
          </el-button>
          <el-button
            v-if="hasPermission('banknote:sendform:check')"
            size="small"
            class="ele-btn-icon"
            @click="checkYes"
            :disabled="!selections.length"
          >
            审核通过
          </el-button>
<!--          <el-button
            v-if="hasPermission('banknote:sendform:check')"
            size="small"
            class="ele-btn-icon"
            @click="checkNo"
            :disabled="!selections.length"
          >
            审核驳回
          </el-button>-->
          <el-button
            v-if="hasPermission('banknote:sendform:scanAudit')"
            size="small"
            class="ele-btn-icon"
            @click="handleEnableScanAudit"
            :disabled="!selections.length"
          >
            开启扫码审核
          </el-button>
          <el-button
            v-if="hasPermission('banknote:sendform:scanAudit')"
            size="small"
            class="ele-btn-icon"
            @click="handleDisableScanAudit"
            :disabled="!selections.length"
          >
            关闭扫码审核
          </el-button>
<!--          <el-button
            v-if="hasPermission('banknote:sendform:scanAudit')"
            size="small"
            class="ele-btn-icon"
            @click="handleAutoEnableScanAudit"
            :disabled="!selections.length"
          >
            自动开启扫码审核
          </el-button>-->
          <el-button
            v-if="hasPermission('banknote:sendform:remove')"
            size="small"
            class="ele-btn-icon"
            @click="remove()"
            :disabled="!selections.length"
          >
            删除
          </el-button>
        </template>

        <!-- 财务核对列 -->
        <template #ifyou="{ row }">
          <el-icon v-if="row.ifyou === 1" style="color: #67c23a; font-size: 18px;">
            <CircleCheckFilled />
          </el-icon>
        </template>

        <!-- 审核状态列 -->
        <template #checkStatus="{ row }">
          <el-tag v-if="row.checkStatus === 1" type="success">yes</el-tag>
          <el-tag v-else type="danger">no</el-tag>
        </template>

        <!-- 扫码审核列 -->
        <template #fullyOpen="{ row }">
          <span v-if="row.fullyOpen === 1" style="color: #67c23a;">已开启</span>
        </template>

        <!-- 操作列 -->
        <template #action="{ row }">
<!--          <el-link
            v-if="hasPermission('banknote:sendform:saveOrUpdate')"
            type="primary"
            :underline="false"
            @click="openEdit(row)"
          >
            修改
          </el-link>
          <el-divider
            v-if="hasPermission('banknote:sendform:saveOrUpdate') && hasPermission('banknote:sendform:list')"
            direction="vertical"
          />
          <el-divider
            v-if="hasPermission('banknote:sendform:saveOrUpdate')"
            direction="vertical"
          />-->
          <el-link
            v-if="hasPermission('banknote:sendform:saveOrUpdate')"
            type="primary"
            :underline="false"
            @click="openEditDetail(row)"
          >
            修改
          </el-link>
        </template>
      </ele-pro-table>
    </ele-card>

    <!-- 编辑弹窗 -->
    <edit v-model="showEdit" :data="current" @done="reload" />

    <!-- 增加钱币对话框 -->
    <add-coin-dialog
      v-model="addCoinVisible"
      :sendnum="selectedSendnum"
      @done="handleAddCoinDone"
    />

    <!-- 详细编辑弹窗 -->
    <edit-detail
      v-model="showEditDetail"
      :sendnum="currentSendnum"
      @done="reload"
    />
  </ele-page>
</template>

<script setup>
import { ref, reactive, computed } from 'vue';
import { ElMessageBox } from 'element-plus/es';
import { EleMessage } from 'ele-admin-plus/es';
import { CircleCheckFilled } from '@element-plus/icons-vue';
import {
  queryPage,
  removes,
  checkYesBatch,
  checkNoBatch,
  checkUnScoredCoins,
  enableScanAudit,
  disableScanAudit,
  autoEnableScanAudit
} from './api';
import Search from './components/search.vue';
import Edit from './components/edit.vue';
import EditDetail from './components/edit-detail.vue';
import { usePermission } from '@/utils/use-permission';

const { hasPermission } = usePermission();

/** 表格实例 */
const tableRef = ref(null);

/** 表格列配置 */
const columns = computed(() => {
  const baseColumns = [];

  // 根据权限决定是否显示选择框列
  if (hasPermission('banknote:sendform:remove') ||
      hasPermission('banknote:sendform:check') ||
      hasPermission('banknote:sendform:addCoin')) {
    baseColumns.push({
      type: 'selection',
      columnKey: 'selection',
      width: 45,
      fixed: 'left'
    });
  }

  // 添加其他列
  baseColumns.push(
    {
      prop: 'sendnum',
      label: '送评单号',
      minWidth: 140,
      slot: 'sendnum'
    },
    {
      prop: 'nickname',
      label: '网名',
      minWidth: 140
    },
    {
      prop: 'rname',
      label: '真实姓名',
      minWidth: 100
    },
    {
      prop: 'coinCount',
      label: '项目数量',
      width: 100,
      align: 'center'
    },
    {
      prop: 'sumfeetw',
      label: '总费用',
      width: 100,
      align: 'center'
    },
    {
      prop: 'inupttime',
      label: '创建时间',
      minWidth: 160,
      formatter: (row) => {
        return row.inupttime || '-';
      }
    },
    {
      prop: 'ifyou',
      label: '财务核对',
      width: 100,
      align: 'center',
      slot: 'ifyou'
    },
    {
      prop: 'checkStatus',
      label: '评单审核',
      width: 100,
      align: 'center',
      slot: 'checkStatus'
    },
    {
      prop: 'fullyOpen',
      label: '扫码审核',
      width: 100,
      align: 'center',
      slot: 'fullyOpen'
    }
  );

  // 根据权限决定是否显示操作列
  if (hasPermission('banknote:sendform:saveOrUpdate') || hasPermission('banknote:sendform:list')) {
    baseColumns.push({
      columnKey: 'action',
      label: '操作',
      width: 200,
      align: 'center',
      slot: 'action'
    });
  }

  return baseColumns;
});

/** 表格选中数据 */
const selections = ref([]);

/** 当前编辑数据 */
const current = ref(null);

/** 是否显示编辑弹窗 */
const showEdit = ref(false);

/** 是否显示详细编辑弹窗 */
const showEditDetail = ref(false);

/** 当前编辑的送评单号 */
const currentSendnum = ref('');

/** 表格搜索参数 */
const lastWhere = reactive({});

/** 增加钱币对话框 */
const addCoinVisible = ref(false);
const selectedSendnum = ref('');

/** 表格数据源 */
const datasource = ({ page, limit, where, orders, filters }) => {
  return queryPage({ ...where, ...orders, ...filters, page, limit });
};

/** 搜索 */
const reload = (where) => {
  selections.value = [];
  if (where) {
    lastWhere.value = where;
    tableRef.value?.reload?.({ page: 1, where });
  } else {
    tableRef.value?.reload?.();
  }
};

/** 打开编辑弹窗 */
const openEdit = (row) => {
  current.value = row ?? null;
  showEdit.value = true;
};

/** 删除 */
const remove = (row) => {
  const rows = row == null ? selections.value : [row];
  if (!rows.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }
  ElMessageBox.confirm(
    '确定要删除"' + rows.map((d) => d.sendnum).join(', ') + '"吗?',
    '系统提示',
    { type: 'warning', draggable: true }
  ).then(() => {
    const loading = EleMessage.loading('请求中..');
    removes(rows.map((d) => d.id)).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {});
};

/** 审核通过 */
const checkYes = () => {
  if (!selections.value.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }

  // 先检查钱币打分情况
  const loading = EleMessage.loading('检查钱币打分情况...');
  const sendnums = selections.value.map(d => d.sendnum);

  checkUnScoredCoins(sendnums).then((result) => {
    loading.close();

    // 检查是否有未打分的钱币
    if (result.hasUnScoredCoins) {
      // 构建详细的错误信息
      let errorMessage = '以下送评单含有未打分钱币，请先完成品相评分：\n\n';

      Object.keys(result.unScoredCoins).forEach(sendnum => {
        const unScoredList = result.unScoredCoins[sendnum];
        errorMessage += `送评单号：${sendnum}（${unScoredList.length}个未打分钱币）\n`;
        unScoredList.forEach(coin => {
          errorMessage += `  • ${coin.serialNumber} - ${coin.coinName1 || '未命名钱币'}\n`;
        });
        errorMessage += '\n';
      });

      errorMessage += `总计：${result.totalUnScoredCount}个钱币未完成打分`;

      ElMessageBox.alert(errorMessage, '钱币打分检查', {
        type: 'warning',
        draggable: true,
        customStyle: {
          width: '600px'
        }
      });
      return;
    }

    // 所有钱币都已打分，继续审核流程
    ElMessageBox.confirm('确定所选项修改为"审核通过√"？', '系统提示', {
      type: 'warning',
      draggable: true
    }).then(() => {
      const approveLoading = EleMessage.loading('请求中..');
      checkYesBatch(sendnums).then((msg) => {
        approveLoading.close();
        EleMessage.success(msg);
        reload();
      }).catch((e) => {
        approveLoading.close();
        EleMessage.error(e.message);
      });
    }).catch(() => {});

  }).catch((e) => {
    loading.close();
    EleMessage.error('检查钱币打分情况失败：' + e.message);
  });
};

/** 审核驳回 */
const checkNo = () => {
  if (!selections.value.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }

  ElMessageBox.confirm('确定所选项修改为"审核不通过x"？', '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    const loading = EleMessage.loading('请求中..');
    const sendnums = selections.value.map(d => d.sendnum);
    checkNoBatch(sendnums).then((msg) => {
      loading.close();
      EleMessage.success(msg);
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {});
};

/** 增加钱币 */
const addCoin = () => {
  if (!selections.value.length || selections.value.length > 1) {
    EleMessage.error('请选择一条送评单记录');
    return;
  }
  selectedSendnum.value = selections.value[0].sendnum;
  addCoinVisible.value = true;
};

/** 增加钱币完成回调 */
const handleAddCoinDone = () => {
  addCoinVisible.value = false;
  reload();
};


/** 打开详细编辑弹窗 */
const openEditDetail = (row) => {
  currentSendnum.value = row.sendnum;
  showEditDetail.value = true;
};

/** 开启扫码审核 */
const handleEnableScanAudit = () => {
  if (!selections.value.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }

  ElMessageBox.confirm('确定开启所选送评单的扫码审核功能？', '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    const loading = EleMessage.loading('请求中..');
    const sendnums = selections.value.map(d => d.sendnum);
    enableScanAudit(sendnums).then((msg) => {
      loading.close();
      EleMessage.success('扫码审核开启成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {});
};

/** 关闭扫码审核 */
const handleDisableScanAudit = () => {
  if (!selections.value.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }

  ElMessageBox.confirm('确定关闭所选送评单的扫码审核功能？', '系统提示', {
    type: 'warning',
    draggable: true
  }).then(() => {
    const loading = EleMessage.loading('请求中..');
    const sendnums = selections.value.map(d => d.sendnum);
    disableScanAudit(sendnums).then((msg) => {
      loading.close();
      EleMessage.success('扫码审核关闭成功');
      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {});
};

/** 自动开启扫码审核 */
const handleAutoEnableScanAudit = () => {
  if (!selections.value.length) {
    EleMessage.error('请至少选择一条数据');
    return;
  }

  ElMessageBox.confirm(
    '系统将检查所选送评单是否满足开启条件（已审核通过、财务已核对、钱币已打分），满足条件的将自动开启扫码审核。确定继续？',
    '自动开启扫码审核',
    {
      type: 'info',
      draggable: true
    }
  ).then(() => {
    const loading = EleMessage.loading('检查条件并开启中...');
    const sendnums = selections.value.map(d => d.sendnum);
    autoEnableScanAudit(sendnums).then((result) => {
      loading.close();

      // 显示详细结果
      let message = result.message;
      if (result.successList && result.successList.length > 0) {
        message += `\n成功开启：${result.successList.join(', ')}`;
      }
      if (result.skippedList && result.skippedList.length > 0) {
        message += `\n跳过：${result.skippedList.join(', ')}`;
      }
      if (result.failedList && result.failedList.length > 0) {
        message += `\n失败：${result.failedList.join(', ')}`;
      }

      ElMessageBox.alert(message, '自动开启结果', {
        type: result.successCount > 0 ? 'success' : 'warning',
        draggable: true
      });

      reload();
    }).catch((e) => {
      loading.close();
      EleMessage.error(e.message);
    });
  }).catch(() => {});
};


</script>

<script>
export default {
  name: 'BankNoteSendform'
};
</script>

<style scoped>
.el-link {
  font-weight: unset !important;
}
</style>
