<template>
  <div class="coin-image-upload">
    <div class="upload-header" v-if="showTitle">
      <h5>{{ title }}</h5>
      <span class="image-count">{{ imageList.length }}/{{ limit }}</span>
    </div>
    
    <div class="upload-container">
      <ele-upload-list
        v-model="imageList"
        :limit="limit"
        :drag="drag"
        :multiple="multiple"
        accept="image/*"
        :item-style="itemStyle"
        :button-style="buttonStyle"
        @upload="handleImageUpload"
        @remove="handleImageRemove"
      >
        <template #default>
          <div class="upload-placeholder">
            <el-icon :size="24"><Upload /></el-icon>
            <div>{{ placeholder }}</div>
          </div>
        </template>
      </ele-upload-list>
    </div>
    
    <!-- 图片预览信息 -->
    <div v-if="showInfo && imageList.length > 0" class="upload-info">
      <el-text size="small" type="info">
        已上传 {{ imageList.length }} 张图片，支持拖拽排序
      </el-text>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Upload } from '@element-plus/icons-vue';
import { EleMessage } from 'ele-admin-plus';
import { ElMessageBox } from 'element-plus';
import { uploadFile } from '@/api/system/file';

const props = defineProps({
  // v-model 绑定的图片数据
  modelValue: {
    type: [String, Array],
    default: null
  },
  // 最大上传数量
  limit: {
    type: Number,
    default: 10
  },
  // 是否支持拖拽
  drag: {
    type: Boolean,
    default: true
  },
  // 是否支持多选
  multiple: {
    type: Boolean,
    default: true
  },
  // 图片项样式
  itemStyle: {
    type: Object,
    default: () => ({ width: '120px', height: '120px' })
  },
  // 上传按钮样式
  buttonStyle: {
    type: Object,
    default: () => ({ width: '120px', height: '120px' })
  },
  // 占位符文本
  placeholder: {
    type: String,
    default: '点击或拖拽上传图片'
  },
  // 标题
  title: {
    type: String,
    default: '图片'
  },
  // 是否显示标题
  showTitle: {
    type: Boolean,
    default: true
  },
  // 是否显示信息
  showInfo: {
    type: Boolean,
    default: true
  },
  // 文件大小限制（MB）
  maxSize: {
    type: Number,
    default: 5
  }
});

const emit = defineEmits(['update:modelValue', 'upload-success', 'remove-success']);

/** 图片列表数据处理 */
const imageList = computed({
  get() {
    if (!props.modelValue) return [];
    
    // 处理不同的数据格式：字符串或数组
    let imageUrls = [];
    if (typeof props.modelValue === 'string') {
      // 如果是字符串，可能是单个URL或逗号分隔的多个URL
      imageUrls = props.modelValue.split(',').filter(url => url.trim());
    } else if (Array.isArray(props.modelValue)) {
      imageUrls = props.modelValue;
    }
    
    // 将URL数组转换为上传组件需要的格式
    return imageUrls.map((url, index) => ({
      id: `img_${index}_${Date.now()}`,
      url: url.trim(),
      name: `图片${index + 1}`,
      status: 'done',
      response: { url: url.trim() }
    }));
  },
  set(value) {
    // 将上传组件格式转换为URL数组
    const urls = value
      .filter(item => item.status === 'done' && (item.url || item.response?.url))
      .map(item => item.url || item.response?.url);
    
    // 根据后端期望的格式保存：如果只有一个URL就保存为字符串，多个URL用逗号分隔
    let result;
    if (urls.length === 0) {
      result = null;
    } else if (urls.length === 1) {
      result = urls[0];
    } else {
      result = urls.join(',');
    }
    
    emit('update:modelValue', result);
  }
});

/** 图片上传处理 */
const handleImageUpload = async (item) => {
  if (!item.file) return;
  
  // 文件类型检查
  if (!item.file.type.startsWith('image/')) {
    EleMessage.error('只能上传图片文件');
    return;
  }
  
  // 文件大小检查
  if (item.file.size / 1024 / 1024 > props.maxSize) {
    EleMessage.error(`图片大小不能超过${props.maxSize}MB`);
    return;
  }
  
  try {
    // 设置上传状态
    item.status = 'uploading';
    
    // 调用上传API
    const result = await uploadFile(item.file);
    
    // 解析返回的数据
    let fileData;
    if (Array.isArray(result)) {
      fileData = result[0];
    } else if (typeof result === 'string') {
      // 如果返回的是JSON字符串，需要解析
      try {
        const parsed = JSON.parse(result);
        fileData = Array.isArray(parsed) ? parsed[0] : parsed;
      } catch (e) {
        console.error('Failed to parse result:', result);
        throw new Error('上传返回数据格式错误');
      }
    } else {
      fileData = result;
    }
    
    // 构建图片URL
    const imageUrl = `/api/file/inline/${fileData.id}`;
    
    // 上传成功
    item.status = 'done';
    item.url = imageUrl;
    item.response = { url: imageUrl, fileData };
    
    EleMessage.success('图片上传成功');
    
    // 更新数据
    updateImageData(imageUrl, 'add');
    
    // 发射上传成功事件
    emit('upload-success', {
      url: imageUrl,
      fileData: fileData,
      item: item
    });
    
  } catch (error) {
    item.status = 'error';
    EleMessage.error('图片上传失败：' + (error.message || '未知错误'));
    console.error('Upload error:', error);
  }
};

/** 图片删除处理 */
const handleImageRemove = (item) => {
  ElMessageBox.confirm('确定要删除这张图片吗？', '确认删除', {
    type: 'warning',
    confirmButtonText: '确定',
    cancelButtonText: '取消'
  }).then(() => {
    const imageUrl = item.url || item.response?.url;
    
    // 更新数据
    updateImageData(imageUrl, 'remove');
    
    EleMessage.success('图片删除成功');
    
    // 发射删除成功事件
    emit('remove-success', {
      url: imageUrl,
      item: item
    });
  }).catch(() => {
    // 用户取消删除
  });
};

/** 更新图片数据 */
const updateImageData = (imageUrl, action) => {
  const currentValue = props.modelValue;
  
  if (action === 'add') {
    if (!currentValue) {
      emit('update:modelValue', imageUrl);
    } else if (typeof currentValue === 'string') {
      emit('update:modelValue', currentValue + ',' + imageUrl);
    } else if (Array.isArray(currentValue)) {
      emit('update:modelValue', [...currentValue, imageUrl]);
    }
  } else if (action === 'remove') {
    if (typeof currentValue === 'string') {
      const urls = currentValue.split(',').filter(url => url.trim() !== imageUrl);
      emit('update:modelValue', urls.length > 0 ? urls.join(',') : null);
    } else if (Array.isArray(currentValue)) {
      const newUrls = currentValue.filter(url => url !== imageUrl);
      emit('update:modelValue', newUrls.length > 0 ? newUrls : null);
    }
  }
};
</script>

<style scoped>
.coin-image-upload {
  width: 100%;
}

.upload-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.upload-header h5 {
  margin: 0;
  color: #303133;
  font-size: 14px;
  font-weight: 500;
}

.image-count {
  color: #909399;
  font-size: 12px;
}

.upload-container {
  width: 100%;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  color: #8c939d;
  font-size: 12px;
}

.upload-placeholder .el-icon {
  margin-bottom: 8px;
}

.upload-info {
  margin-top: 8px;
  text-align: center;
}

:deep(.ele-upload-list) {
  gap: 10px;
}

:deep(.ele-upload-item) {
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid #dcdfe6;
  transition: all 0.3s;
}

:deep(.ele-upload-item:hover) {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.2);
}

:deep(.ele-upload-item img) {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

:deep(.ele-upload-button) {
  border: 2px dashed #d9d9d9;
  border-radius: 6px;
  background-color: #fafafa;
  transition: all 0.3s;
}

:deep(.ele-upload-button:hover) {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 上传进度样式 */
:deep(.ele-upload-item[data-status="uploading"]) {
  border-color: #409eff;
  background-color: #f0f9ff;
}

/* 上传失败样式 */
:deep(.ele-upload-item[data-status="error"]) {
  border-color: #f56c6c;
  background-color: #fef0f0;
}
</style>
