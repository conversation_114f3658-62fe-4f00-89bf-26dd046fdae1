<template>
  <ele-drawer
    size="76%"
    :title="header ? header : '人员选择'"
    :append-to-body="true"
    :destroy-on-close="true"
    :close-on-click-modal="false"
    style="max-width: 100%"
    :model-value="modelValue"
    :body-style="{ paddingBottom: '8px', marginTop: 0 }"
    :title-style="{ fontSize: '16px' }"
    @update:modelValue="updateModelValue"
  >
    <div class="treeTransfer" :style="{ height: pageHeight + 'px' }">
      <!-- 左边 -->
      <div class="leftTree">
        <div class="list">
          <el-tabs v-model="activeName">
            <el-tab-pane
              v-for="tab in tableTabs"
              :label="tab.title"
              :name="tab.id"
            ></el-tab-pane>
          </el-tabs>
          <!-- 搜索 -->
          <div class="left_input">
            <el-row :gutter="10">
              <el-col :span="8">
                <el-checkbox
                  :indeterminate="isIndeterminate[activeName]"
                  v-model="checkAll[activeName]"
                  @change="handleCheckAllChange"
                  >全选 [{{ leftOperation[activeName]?.length ?? 0 }}/{{
                    currentName === 'person' &&
                    currentTab[0].userType === 'student'
                      ? total
                      : rDataList[activeName]?.length ?? 0
                  }}]
                </el-checkbox>
              </el-col>
              <el-col :span="16">
                <el-input
                  v-model="leftSearchText"
                  placeholder="请输入您要查询的关键字"
                  clearable
                  :suffix-icon="Search"
                  size="small"
                  @clear="onSearchLeft"
                  @keyup.enter="onSearchLeft"
                  @change="onSearchLeft"
                />
              </el-col>
            </el-row>
            <searchPreview
              v-if="
                currentName === 'person' && currentTab[0].userType === 'student'
              "
              @onDone="onDoneSearch"
            />
          </div>
          <div style="overflow: auto; flex: 1">
            <el-checkbox-group
              style="margin-top: 10px"
              v-model="leftOperation[activeName]"
              :max="selectMaxNubmer"
            >
              <el-row>
                <el-col
                  v-for="(item, index) in rDataList[activeName]"
                  :key="index"
                >
                  <el-checkbox
                    @change="onCheckLeft(item)"
                    :value="item.dictDataCode"
                    >{{ item.dictDataName }}
                  </el-checkbox>
                </el-col>
              </el-row>
            </el-checkbox-group>
          </div>
          <!-- 分页按钮 -->
          <el-pagination
            v-if="
              currentName === 'person' && currentTab[0].userType === 'student'
            "
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="currentPage"
            :page-sizes="[60, 80, 100, 200]"
            :page-size="pageSize"
            :total="total"
            :page-count="total / pageSize"
            size="small"
            :background="true"
            layout=" sizes, prev,  next, jumper"
          >
          </el-pagination>
        </div>
      </div>
      <!-- 中间按钮 -->
      <div class="btnDiv">
        <div class="mg10"></div>
      </div>
      <!-- 右边 -->
      <div class="rightTree">
        <div class="list">
          <ele-card
            header="已所选范围"
            :body-style="{
              padding: '5px 5px 10px 5px!important',
              height: pageHeight - 88 + 'px',
              overflow: 'auto',
              flex: 1
            }"
          >
            <template #extra>
              <el-checkbox
                v-model="isSync"
                v-if="paramMode !== 'zzbdweh' && paramMode !== 'base'"
              >
                是否并联
              </el-checkbox>
            </template>
            <template #default>
              <div v-if="paramMode === 'zzbdweh'">
                <ele-alert
                  title="支持元素拖拽进行排序"
                  show-icon
                  style="margin-bottom: 12px"
                  :closable="false"
                />
                <template v-for="lgroup in leftGroupData">
                  <el-divider content-position="left"
                    >{{ lgroup.type }}
                  </el-divider>
                  <el-checkbox-group v-model="leftOperationArrayCheck">
                    <vue-draggable
                      v-model="lgroup.list"
                      item-key="value"
                      :animation="300"
                      :set-data="() => void 0"
                      class="demo-grid"
                    >
                      <template #item="{ element }">
                        <el-checkbox
                          border
                          class="demo-grid-item"
                          @change="onCheckRight(element)"
                          :value="element.dictDataCode"
                          >{{
                            element.dictDataName
                              ? element.dictDataName
                              : element.name
                          }}
                        </el-checkbox>
                      </template>
                    </vue-draggable>
                  </el-checkbox-group>
                </template>
              </div>
              <div v-else>
                <el-checkbox-group
                  style="margin-top: 10px"
                  v-model="leftOperationArrayCheck"
                >
                  <el-row>
                    <!--                    paramMode==='base'?6:-->
                    <el-col
                      :span="12"
                      v-for="(item, index) in leftOperationArray"
                      :key="index"
                    >
                      <el-checkbox
                        @change="onCheckRight(item)"
                        :value="item.dictDataCode"
                        >{{ item.dictDataName ? item.dictDataName : item.name }}
                      </el-checkbox>
                    </el-col>
                  </el-row>
                </el-checkbox-group>
              </div>
            </template>
          </ele-card>
        </div>
      </div>
    </div>
    <template #footer>
      <el-button size="small" @click="updateModelValue(false)">取消</el-button>
      <el-button type="primary" size="small" plain @click="save">
        保存
      </el-button>
    </template>
  </ele-drawer>
</template>
<script setup>
  import { ref, onMounted, watch, computed, nextTick } from 'vue';
  import { Search } from '@element-plus/icons-vue';
  import { EleMessage } from 'ele-admin-plus';
  import { useUserStore } from '@/store/modules/user';
  import { storeToRefs } from 'pinia';
  import { useDictData } from '@/utils/use-dict-data';
  import { mergeUnique, removeSomeData } from '@/utils/common';
  import { getSelectorConfig } from './api/index';
  import { groupArr } from '@/utils/common';
  import { ElPagination } from 'element-plus';
  import VueDraggable from 'vuedraggable';

  import SearchPreview from './search-preview.vue';
  import { queryPage } from '@/views/personInfo/api';
  import { getDicFieldValueByUrl } from '@/views/system/sphfw/dictionary-field/api/index.js';

  const props = defineProps({
    /** 弹窗是否打开 */
    modelValue: Boolean,
    /**功能业务类型*/
    paramMode: String, //base=通用业务类型
    selectMaxNubmer: {
      type: Number,
      default: '—'
    }, //指定可以选择的数量
    header: String,
    projectId: String, //项目ID
    userType: String, //用户类型
    perSelectedData: Array
  });
  const leftSearchText = ref('');
  // 左侧数据
  const leftOperation = ref({});
  // 右侧数据
  const leftOperationArray = ref([]);

  // 定义emit
  const emits = defineEmits(['done', 'update:modelValue', 'change']);

  const isIndeterminate = ref({});
  const checkAll = ref({});

  /** 更新modelValue */
  const updateModelValue = (value) => {
    emits('update:modelValue', value);
  };

  const userStore = useUserStore();
  const { dicts } = storeToRefs(userStore);

  const currentUserType = ref();
  const currentTab = ref();
  const currentName = ref(null);
  const activeName = ref();
  const tableTabs = ref([]);
  const isSync = ref(false);

  /**
   * 项目基础设置，人员选择器分组处理
   * paramMode==='zzbdweh'
   * */
  const leftGroupData = ref([]);

  const total = ref(null); // 假设总数据量为100
  const pageSize = ref(60); // 每页显示10条数据
  const currentPage = ref(1); // 当前页码
  const queryParams = ref({});
  const rDataList = ref({});

  // 计算属性，根据当前页码和每页显示条数获取当前页的数据
  const queryStudentList = () => {
    if (currentUserType.value) {
      let obj = { page: currentPage.value, limit: pageSize.value };
      let newObj = Object.assign(obj, queryParams.value);
      queryPage(currentUserType.value, newObj)
        .then((resData) => {
          total.value = resData.count;
          let resList = resData.list;
          if (resList.length > 0) {
            let listData = [];
            resList.forEach((e) => {
              listData.push({
                dictDataCode: e.xgh,
                dictDataName: e.xm,
                userType: e.userType
              });
            });
            // console.log(listData);
            rDataList.value[activeName.value] = listData;
          } else {
            rDataList.value[activeName.value] = [];
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });
    }
  };

  const onDoneSearch = (data) => {
    queryParams.value = data;
    queryStudentList();
  };

  const handleTabChange = (tabName) => {
    currentTab.value = tableTabs.value.filter((obj) => obj.id === tabName);
    currentName.value =
      currentTab.value.length > 0 ? currentTab.value[0].name : null;
    currentUserType.value = currentTab.value.length > 0 ? currentTab.value[0].userType : null
    // console.log(tabName)
    // console.log(tableTabs.value)
    // console.log(currentTab.value)
    // console.log(leftOperation["value"])
    // console.log(rDataList["value"])
    if (currentName.value === 'person') {
      /** 表格数据源 */
      queryStudentList();
    } else {
      let tab = currentTab.value[0];
      let queryParams = {};
      if (tab.columns) {
        queryParams = {
          params: JSON.parse(tab.columns)
        };
      }
      if (tab.url) {
        queryParams = {
          dictFieldUrl: tab.url,
          params: { userType: tab?.userType }
        };
      }
      queryParams.userType = tab?.userType;

      getDicFieldValueByUrl(tab.url, queryParams.params)
        .then((list) => {
          let valueField = queryParams?.valueField ?? 'id';
          let textField = queryParams?.textField ?? 'name';
          if (list) {
            let result = [];
            list.forEach((resData) => {
              result.push({
                dictDataCode: resData[valueField],
                dictDataName: resData[textField],
                userType: tab?.userType,
                ...resData
              });
            });

            rDataList['value'][tabName] = result;
            // userStore.setDicts(result, code);
          }
        })
        .catch((e) => {
          EleMessage.error(e.message);
        });

      // // console.log(queryParams)
      // useDictData([tabName], queryParams);
      //
      // nextTick(() => {
      //   const { dicts } = storeToRefs(userStore);
      //   if (!dicts.value[tabName]) {
      //     useDictData([tabName], queryParams);
      //   }
      //   console.log(dicts.value, tabName);
      //   console.log(dicts.value[tabName]);
      //   rDataList['value'][tabName] = dicts.value[tabName];
      // console.log(rDataList['value']);
      // });
    }

    // }
  };

  watch(
    activeName,
    (newVal) => {
      if (newVal) handleTabChange(newVal);
    },
    { immediate: true }
  );
  // watch(
  //   tableTabs.value,
  //   (tabs) => {
  //     console.log(":tableTabs.value====", tabs.length)
  //     if (tabs) {
  //       tabs.forEach(t => {
  //         handleTabChange(t)
  //       })
  //     }
  //   },
  //   {immediate: true}
  // )

  watch(
    currentPage,
    (newVal) => {
      if (newVal) queryStudentList();
    },
    { immediate: true }
  );

  watch(
    pageSize,
    (newVal) => {
      if (newVal) queryStudentList();
    },
    { immediate: true }
  );

  // 分页大小改变时的回调
  const handleSizeChange = (val) => {
    pageSize.value = val;
  };

  // 当前页改变时的回调
  const handleCurrentChange = (val) => {
    currentPage.value = val;
  };

  /** 是否全选 */
  const handleCheckAllChange = (val) => {
    isIndeterminate['value'][activeName.value] = false;
    const leftData = rDataList['value'][activeName.value];
    let allDataCode = [];
    let allData = [];
    if (leftData.length > 0) {
      leftData.forEach((e) => {
        allDataCode.push(e.dictDataCode);
        // allData.push(e)
        allData.push({
          name: e.dictDataName,
          value: e.dictDataCode,
          configId: activeName.value,
          configKey: currentTab.value?.[0].key,
          configName: currentTab.value?.[0].name,
          configTitle: currentTab.value?.[0].title,
          ...e
        });
      });
    }
    let newArray = [];
    // if (val) {
    //   leftOperation.value[activeName.value] = allDataCode;
    // } else {
    //   leftOperation.value[activeName.value] = [];
    // }
    if (val) {
      leftOperation['value'][activeName.value] = allDataCode;
      newArray = mergeUnique(allData, leftOperationArray.value, 'dictDataCode');
    } else {
      leftOperation.value[activeName.value] = [];
      newArray = removeSomeData(
        allData,
        leftOperationArray.value,
        'dictDataCode'
      );
    }
    leftOperationArray.value = newArray;

    if (props.paramMode === 'zzbdweh') {
      let isExistGroupData = leftGroupData.value.filter(
        (obj) => obj.type === currentTab.value?.[0].title
      );
      if (isExistGroupData.length > 0) {
        isExistGroupData.forEach((v) => {
          if (v.type === currentTab.value?.[0].title) {
            if (val) {
              //全选，添加右侧分组数组
              v.list = allData;
            } else {
              //全不选，删除右侧分组数组
              leftGroupData.value.splice(leftGroupData.value.indexOf(v), 1);
            }
          }
        });
      } else {
        leftGroupData.value.push({
          type: currentTab.value?.[0].title,
          list: allData,
          size: allData.length
        });
      }
    }
    emits('done', leftOperationArray.value);
  };

  const leftOperationArrayCheck = computed(() => {
    let allDataCode = [];
    if (leftOperationArray.value.length > 0) {
      leftOperationArray.value.forEach((e) => {
        allDataCode.push(e.dictDataCode);
      });
    }
    return allDataCode;
  });

  //左侧选中
  const onCheckLeft = (e) => {
    let newData = {
      name: e.dictDataName,
      value: e.dictDataCode,
      configId: activeName.value,
      configKey: currentTab.value?.[0].key,
      configName: currentTab.value?.[0].name,
      configTitle: currentTab.value?.[0].title,
      ...e
    };
    let tabName = activeName.value;
    let checkedCount = leftOperation['value'][tabName]
      ? leftOperation['value'][tabName].length
      : 0;
    let dataCount = rDataList['value'][tabName]
      ? rDataList['value'][tabName].length
      : 0;
    checkAll['value'][tabName] = checkedCount === dataCount;
    isIndeterminate['value'][tabName] =
      checkedCount > 0 && checkedCount < dataCount;
    let isExist = leftOperationArray.value.filter(
      (obj) => obj.dictDataCode === e.dictDataCode
    );
    if (isExist.length > 0) {
      leftOperationArray.value.forEach((v) => {
        if (v.dictDataCode === e.dictDataCode) {
          leftOperationArray.value.splice(
            leftOperationArray.value.indexOf(v),
            1
          );
        }
      });
    } else {
      leftOperationArray.value.push(newData);
    }

    if (props.paramMode === 'zzbdweh') {
      let isExistGroupData = leftGroupData.value.filter(
        (obj) => obj.type === newData.configTitle
      );
      if (isExistGroupData.length > 0) {
        isExistGroupData.forEach((v) => {
          if (v.type === newData.configTitle) {
            if (isExist.length > 0) {
              //左侧单个点击取消选择
              v.list.forEach((vl) => {
                if (vl.dictDataCode === e.dictDataCode) {
                  v.list.splice(v.list.indexOf(vl), 1);
                  if (v.list.length <= 0) {
                    //list为空删除右侧分组数组
                    leftGroupData.value.splice(
                      leftGroupData.value.indexOf(vl),
                      1
                    );
                  }
                }
              });
            } else {
              //左侧单个点击选择，给右侧组赋值
              v.list.push(newData);
            }
          }
        });
      } else {
        //左侧单个点击选择，给右侧组赋值---首次
        leftGroupData.value.push({
          type: currentTab.value?.[0].title,
          list: [newData]
        });
      }
    }
  };

  // 右侧选中
  const onCheckRight = (e) => {
    let isExist = leftOperationArray.value.filter(
      (obj) => obj.dictDataCode === e.dictDataCode
    );
    if (isExist.length > 0) {
      leftOperationArray.value.forEach((v) => {
        if (v.dictDataCode === e.dictDataCode) {
          leftOperationArray.value.splice(
            leftOperationArray.value.indexOf(v),
            1
          );
        }
      });
      tableTabs.value.forEach((tab) => {
        if (
          leftOperation['value'][tab.id] &&
          leftOperation['value'][tab.id].length > 0
        ) {
          const leftData = leftOperation['value'][tab.id];
          let isExistCheck = leftData.filter((obj) => obj === e.dictDataCode);
          if (isExistCheck.length > 0) {
            leftOperation['value'][tab.id].forEach((v) => {
              if (v === e.dictDataCode) {
                leftOperation['value'][tab.id].splice(
                  leftOperation['value'][tab.id].indexOf(v),
                  1
                );
              }
            });
          }
        }
      });
    }
    if (props.paramMode === 'zzbdweh') {
      let isExistGroupData = leftGroupData.value.filter(
        (obj) => obj.type === e.configTitle
      );
      if (isExistGroupData.length > 0) {
        isExistGroupData[0].list.forEach((v) => {
          if (v.dictDataCode === e.dictDataCode) {
            isExistGroupData[0].list.splice(
              isExistGroupData[0].list.indexOf(v),
              1
            );
            if (isExistGroupData[0].list.length <= 0) {
              //删除右侧分组数组
              leftGroupData.value.splice(leftGroupData.value.indexOf(v), 1);
            }
          }
        });
      }
    }
    let tabName = activeName.value;
    let checkedCount = leftOperation['value'][tabName]
      ? leftOperation['value'][tabName].length
      : 0;
    let dataCount = rDataList['value'][tabName]
      ? rDataList['value'][tabName].length
      : 0;
    checkAll['value'][tabName] = checkedCount === dataCount;
    isIndeterminate['value'][tabName] =
      checkedCount > 0 && checkedCount < dataCount;
    emits('done', leftOperationArray.value);
  };

  const save = () => {
    updateModelValue(false);
    emits('done', leftOperationArray.value);
  };

  const onSearchLeft = () => {
    if (leftSearchText.value) {
      rDataList['value'][activeName.value] = rDataList['value'][
        activeName.value
      ]?.filter((obj) => obj.dictDataName.includes(leftSearchText.value));
    } else {
      if (currentName.value === 'person') {
        queryStudentList();
      } else {
        rDataList['value'][activeName.value] = dicts.value[currentName.value];
      }
    }
  };

  const querySelectorConfig = () => {
    let obj = {
      paramMode: props?.paramMode,
      userType: props?.userType
    };
    getSelectorConfig(obj)
      .then((resData) => {
        if (resData.length > 0) {
          activeName.value = resData[0].id;
          tableTabs.value = resData;
          // tableTabs.value.forEach(tab => {
          //   handleTabChange(tab.id)
          // })
        }
      })
      .catch((e) => {
        EleMessage.error(e.message);
      });
  };

  const pageHeight = ref(0);
  onMounted(() => {
    // 获取页面高度
    pageHeight.value =
      document.querySelector('.ele-admin-content').clientHeight - 30;
  });

  watch(
    () => props.modelValue,
    (modelValue) => {
      if (modelValue) {
        querySelectorConfig();
        /** 人员选择器页面回显 */
        const resDataChk = props.perSelectedData;
        if (resDataChk) {
          const resData = groupArr(resDataChk, 'configId');
          if (resData.length > 0) {
            resData.forEach((e) => {
              leftOperation['value'][e.type] = e.list.map((item) => item.value);
            });
          }
          resDataChk.forEach((e) => {
            e.dictDataCode = e.value;
            e.dictDataName = e.name;
          });
          leftOperationArray.value = resDataChk;
        }
      }
    }
  );
</script>

<style lang="scss" scoped>
  .treeTransfer {
    display: flex;
    height: 98%;
    width: 100%;

    .btnDiv {
      flex: 1;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .mg10 {
        padding: 3px;

        :deep(svg),
        :deep(.el-icon) {
          height: 2em !important;
          width: 2em !important;
        }
      }
    }

    .leftTree {
      width: 60%;
      box-sizing: border-box;
      padding: 5px 5px;
      border: 1px solid #f5f5f5;
      box-shadow: 5px 5px 10px rgb(0 0 0 / 4%);
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px;
      color: #828282;

      .list {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }

    .rightTree {
      width: 60%;
      box-sizing: border-box;
      padding: 5px 5px;
      border: 1px solid #f5f5f5;
      box-shadow: 5px 5px 10px rgb(0 0 0 / 4%);
      border-bottom-left-radius: 7px;
      border-bottom-right-radius: 7px;
      color: #828282;

      .list {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }

    .left_lowline {
      display: flex;
      align-items: center;
    }

    .right_lowline {
      display: flex;
      align-items: center;
    }
  }

  .el-checkbox {
    font-weight: unset !important;
  }

  .el-checkbox__label {
    vertical-align: text-top;
    white-space: normal;
    word-break: break-all;
    width: 150px;
    padding-left: 3px;
    line-height: 20px !important;
  }

  .demo-grid {
    display: grid;
    grid-gap: 8px;
    grid-template-columns: repeat(3, 1fr);
  }

  .demo-grid-item {
    width: 99%;
    border-radius: 4px;
    cursor: move;

    &.sortable-ghost {
      opacity: 0;
    }
  }
</style>
