<template>
  <SetPerSelector :paramMode="paramMode"
                  :hearder="hearder"
                  :pageHeight="pageHeight"/>
</template>
<script setup>
import SetPerSelector from '@/components/PerSelector/components/set-per-selector.vue'

const props = defineProps({
  /** 弹窗是否打开 */
  modelValue: Boolean,
  /**功能业务类型*/
  paramMode: String,
  hearder: String,
  pageHeight: Number,
  perSelectedData: Array,
});
</script>

