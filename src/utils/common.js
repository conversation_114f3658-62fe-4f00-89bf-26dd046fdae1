import {removeToken} from '@/utils/token-util';
import {removeCurrentRole} from '@/utils/current-role-util';
import {checkLogout} from '@/api/login';
import {EleMessage} from "ele-admin-plus";

/**
 * 退出登录
 * @param route 是否使用路由跳转
 * @param from 登录后跳转的地址
 * @param push 路由跳转方法
 */
export function logout(route, from, push) {
    checkLogout().then(() => {
        removeToken();
        removeCurrentRole();
        if (route && push) {
            push({
                path: '/login',
                query: from ? {from: encodeURIComponent(from)} : void 0
            });
            return;
        }
        // 这样跳转避免再次登录重复注册动态路由
        const BASE_URL = import.meta.env.BASE_URL;
        const url = BASE_URL + 'login'; // hash 路由模式使用 '#/login'
        location.replace(from ? `${url}?from=${encodeURIComponent(from)}` : url);
    }).catch((e) => {
        EleMessage.error(e.message);
    });
}

/**
 * 下载文件
 * @param data 二进制数据
 * @param name 文件名
 * @param type 文件类型
 */
export function download(data, name, type) {
    const blob = new Blob([data], {type: type || 'application/octet-stream'});
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = name;
    a.style.display = 'none';
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
}

/**
 * 参数转url字符串
 * @param params 参数
 * @param url 需要拼接参数的地址
 */
export function toURLSearch(params, url) {
    if (typeof params !== 'object' || params == null) {
        return '';
    }
    const result = transformParams(params)
        .map((d) => `${encodeURIComponent(d[0])}=${encodeURIComponent(d[1])}`)
        .join('&');
    if (!url) {
        return result;
    }
    return (url.includes('?') ? `${url}&` : `${url}?`) + result;
}

/**
 * 参数转表单数据
 * @param params 参数
 */
export function toFormData(params) {
    const formData = new FormData();
    if (typeof params !== 'object' || params == null) {
        return formData;
    }
    transformParams(params).forEach((d) => {
        formData.append(d[0], d[1]);
    });
    return formData;
}

/**
 * get请求处理数组和对象类型参数
 * @param params 参数
 */
export function transformParams(params) {
    const result = [];
    if (params != null && typeof params === 'object') {
        Object.keys(params).forEach((key) => {
            const value = params[key];
            if (value != null && value !== '') {
                //表单多附件上传处理
                if (Array.isArray(value) && value.length && typeof value[0] === 'object') {
                    // && isBlobFile(value[0].file)
                    value.forEach((v) => {
                        if (v.status !== 'done') result.push([key, v.file]);
                    })
                } else if (typeof value === 'object' && !isBlobFile(value)) {
                    getObjectParamsArray(value).forEach((item) => {
                        result.push([`${key}${item[0]}`, item[1]]);
                    });
                } else {
                    result.push([key, value]);
                }
            }
        });
    }
    return result;
}

/**
 * 对象转参数数组
 * @param obj 对象
 */
export function getObjectParamsArray(obj) {
    const result = [];
    Object.keys(obj).forEach((key) => {
        const value = obj[key];
        if (value != null && value !== '') {
            const name = `[${key}]`;
            if (typeof value === 'object' && !isBlobFile(value)) {
                getObjectParamsArray(value).forEach((item) => {
                    result.push([`${name}${item[0]}`, item[1]]);
                });
            } else {
                result.push([name, value]);
            }
        }
    });
    return result;
}

/**
 * 判断是否是文件
 * @param obj 对象
 */
export function isBlobFile(obj) {
    return obj != null && (obj instanceof Blob || obj instanceof File);
}

/**
 * 将两个多维数组合并，并使用Set对象去除重复的子数组
 * @param arr1
 * @param arr2
 * @returns {*[]}
 */
export function mergeUnique(arr1, arr2, keyword) {
    const array = [...arr1, ...arr2];
    const map = new Map();
    const result = array.filter((item) => {
        const k = item[keyword];
        // 如果map中不存在当前关键字，则保留该对象，并将其添加到map中
        const exists = map.has(k);
        map.set(k, true);
        return !exists;
    });
    return result;
}

/**使用 Set 对象去除重复项：*/
export function removeDuplicates(arr1, arr2) {
    const set1 = new Set(arr1);
    const set2 = new Set(arr2);
    const difference = new Set([...set1].filter(x => !set2.has(x)));
    const result = [...difference];
    return result
}


/**
 * 两个数组取相同数据，返回name
 * @param arr1
 * @param arr2
 * @param type 取值字段名
 * @returns {string}
 */
export function transformDicDataName(arr1, arr2, type) {
    let arraySome = []
    let newStr = ''
    if (arr1?.length > 0 && arr2.length > 0) {
        arraySome = arr1.filter(item => arr2.some(ele => {
            if (type) {
                return ele[type] === item.dictDataCode
            } else {
                return ele === item.dictDataCode
            }
        }));
        let arraySomeMap = arraySome.map(item => {
            return item.dictDataName;
        })
        newStr = arraySomeMap.join()
    }
    return newStr;
}


/**
 * 从arr2中删除在arr1中存在的数据
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function removeSomeData(arr1, arr2, type) {
    let arrayDiffMap = arr2.filter((item1) => !arr1.some((item2) => item1[type] === item2[type]))
    return arrayDiffMap;
}

/**
 * 两个数组取相同数据，返回code
 * @param arr1
 * @param arr2
 * @param type
 * @returns {[]}
 */
export function transformDicDataCode(arr1, arr2, type) {
    let arraySome = []
    let arraySomeMap = []
    if (arr2.length > 0) {
        arraySome = arr1.filter(item => arr2.some(ele => {
            if (type) {
                return ele[type] === item.dictDataCode
            } else {
                return ele === item.dictDataCode
            }
        }));
        arraySomeMap = arraySome.map(item => {
            return item.dictDataCode;
        })
    }
    return arraySomeMap;
}

/**
 * 向数组指定下标添加元素
 * @param array
 * @param element
 * @param index
 * @returns {*}
 */
export function insertAtIndex(array, element, index) {
    return array.splice(index, 0, element);
}

/**
 * 生成随机字符串
 * 使用Math.random()函数生成随机数，并将其转换为字符串。然后使用slice()方法截取需要的长度。
 * @param length
 * @returns {string}
 */
export function generateRandomString(length) {
    let result = '';
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';

    for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
    }

    return result;
}

/** 判断是否是图片文件 */
export function isImageFile(item) {
    return (typeof item.contentType === 'string' && item.contentType.startsWith('image/') ? true : false);
};

/** js数组通过某个字段进行分组 */
export function groupArr(list, field, fieldId) {
    let fieldList = [], att = [];
    list.map((e) => {
        fieldList.push(e[field])
    });
    //数组去重
    fieldList = fieldList.filter((e, i, self) => {
        return self.indexOf(e) == i
    });
    for (let j = 0; j < fieldList.length; j++) {
        //过滤出匹配到的数据
        let arr = list.filter((e) => {
            // return e.sttype == fieldList[j];
            return e[field] == fieldList[j];
        });
        att.push({
            // type: arr[0].sttype,
            type: arr[0][field],
            fieldId: fieldId ? arr[0][fieldId] : '',
            listLength: arr.length,
            list: arr,
        })
    }
    return att;
}

/** sort方法根据数组中对象的某一个属性值进行排序 */
export function compare(property) {
    return function (a, b) {
        var value1 = a[property];
        var value2 = b[property];
        return value1 - value2;
    }
}

/**
 * 遍历一个树形结构并获取所有子节点
 * @param node childNode为Object对象
 * @returns {*[]}
 */
// export function traverseTree(node,) {
//     let result = [];
//     (function recurse(currentNode) {
//         if (currentNode) {
//             console.log(currentNode.type)
//             // 然后递归遍历这个子节点
//             // 如果children是单个对象，加入结果数组
//             if (currentNode.childNode) {
//                 let newNodeData = {
//                     ...currentNode.nodeUserList,
//                     id: currentNode.id,
//                     nodeName: currentNode.nodeName,
//                     settype: currentNode.settype,
//                     type: currentNode.type,
//                     error: currentNode.error,
//                     childNode: currentNode.childNode,
//                 }
//                 result.push(newNodeData);
//                 recurse(currentNode.childNode);
//                 // } else {
//                 //     result.splice(-1, 1); // 删除最后一个内嵌数组
//             }
//         }
//     })(node); // 调用时立即执行函数
//     return result;
// }

export function traverseTree(node,) {
    let result = [];
    (function recurse(currentNode) {
        if (currentNode) {
            // 然后递归遍历这个子节点
            // 如果children是单个对象，加入结果数组
            if (currentNode.childNode) {
                let newNodeData = {
                    ...currentNode.nodeUserList,
                    id: currentNode.id,
                    nodeName: currentNode.nodeName,
                    settype: currentNode.settype,
                    type: currentNode.type,
                    error: currentNode.error,
                    // childNode: currentNode.childNode,
                }
                result.push(newNodeData);
                recurse(currentNode.childNode);
                // } else {
                //     result.splice(-1, 1); // 删除最后一个内嵌数组
            }
        }
    })(node); // 调用时立即执行函数
    return result;
}

/**
 * 递归数组，子节点childNode
 * @param arr
 * @param n
 * @returns {*}
 */
export function recursiveArrayFunction(arr, n) {
    if (n == (arr.length - 1)) {
        return arr[n]
    }
    arr[n].childNode = recursiveArrayFunction(arr, n + 1);
    return arr[n];
}


