/**
 * axios实例
 */
import axios from 'axios';
import { unref } from 'vue';
import { ElMessageBox } from 'element-plus/es';
import { API_BASE_URL } from '@/config/setting';
import router from '@/router';
import { getToken, removeToken, setToken } from './token-util';
import { getCurrentRole, removeCurrentRole } from './current-role-util';
import { toURLSearch } from './common';

/** 创建axios实例 */
const service = axios.create({
    baseURL: API_BASE_URL
});

/**
 * 添加请求拦截器
 */
service.interceptors.request.use(
    (config) => {
        // 添加token到header
        const token = getToken();
        if (token && config.headers) {
            config.headers['Authorization'] = token;
        }
        /** 当前用户角色*/
        const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
        if (currentRoles && config.headers) {
            config.headers['roleId'] = currentRoles.roleId;
        }
        // get请求处理数组和对象类型参数
        if (config.method === 'get' && config.params) {
            config.url = toURLSearch(config.params, config.url);
            config.params = {};
        }
        return config;
    },
    (error) => {
        return Promise.reject(error);
    }
);

/**
 * 添加响应拦截器
 */
service.interceptors.response.use(
    (res) => {
        // 登录过期处理，接口返回401直接到登录页重新登录
        if (res.data?.code === 401) {
            const {path, fullPath} = unref(router.currentRoute);
            ElMessageBox.close();
            ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
                confirmButtonText: '重新登录',
                callback: (action) => {
                    if (action === 'confirm') {
                        // logout(false, fullPath);
                        removeToken();
                        removeCurrentRole();
                        // 这样跳转避免再次登录重复注册动态路由
                        const BASE_URL = import.meta.env.BASE_URL;
                        const url = BASE_URL + 'login'; // hash 路由模式使用 '#/login'
                        location.replace(url);
                    }
                },
                type: 'warning',
                draggable: true
            });
            // }
            return Promise.reject(new Error(res.data.message));
        }
        // 续期token
        const newToken = res.headers['authorization'];
        if (newToken) {
            setToken(newToken);
        }
        return res;
    },
    (error) => {
        return Promise.reject(error);
    }
);

/**
 * 流式请求处理
 * @param {string} url - 请求地址
 * @param {object} options - 请求配置
 * @returns {Promise<Response>} - fetch响应对象
 */
const streamRequest = async (url, options = {}) => {
    // 获取认证信息
    const token = getToken();
    const currentRoles = getCurrentRole() ? JSON.parse(getCurrentRole()) : {};
    
    // 构建请求头
    const headers = {
        'Content-Type': 'application/json',
        ...(token && { 'Authorization': token }),
        ...(currentRoles?.roleId && { 'roleId': currentRoles.roleId }),
        ...options.headers
    };

    // 处理URL
    let finalUrl = url.startsWith('/') ? `${API_BASE_URL}${url}` : `${API_BASE_URL}/${url}`;
    if (options.method === 'GET' && options.params) {
        finalUrl = toURLSearch(options.params, finalUrl);
    }

    try {
        const response = await fetch(finalUrl, {
            ...options,
            headers
        });

        if (!response.ok) {
            // 处理401等错误
            if (response.status === 401) {
                const {path, fullPath} = unref(router.currentRoute);
                ElMessageBox.close();
                ElMessageBox.alert('登录状态已过期, 请退出重新登录!', '系统提示', {
                    confirmButtonText: '重新登录',
                    callback: (action) => {
                        if (action === 'confirm') {
                            removeToken();
                            removeCurrentRole();
                            const BASE_URL = import.meta.env.BASE_URL;
                            const url = BASE_URL + 'login';
                            location.replace(url);
                        }
                    },
                    type: 'warning',
                    draggable: true
                });
            }
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        // 处理token续期
        const newToken = response.headers.get('authorization');
        if (newToken) {
            setToken(newToken);
        }

        return response;
    } catch (error) {
        ElMessageBox.close();
        ElMessageBox.alert(error.message || '请求失败', '错误', {
            type: 'error',
            draggable: true
        });
        throw error;
    }
};

export { service as default, streamRequest };
