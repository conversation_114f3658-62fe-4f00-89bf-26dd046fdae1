/**
 * currentRole操作封装
 */
import { CURRENT_ROLE_CACHE_NAME } from '@/config/setting';

/**
 * 获取缓存的当前角色
 */
export function getCurrentRole() {
    const currentRole = localStorage.getItem(CURRENT_ROLE_CACHE_NAME);
    if (!currentRole) {
        return sessionStorage.getItem(CURRENT_ROLE_CACHE_NAME);
    }
    return currentRole;
}

/**
 * 缓存当前角色
 * @param currentRole
 */
export function setCurrentRole(currentRole) {
    removeCurrentRole();
    if (currentRole) {
        sessionStorage.setItem(CURRENT_ROLE_CACHE_NAME, JSON.stringify(currentRole));
    }
}

/**
 * 移除当前角色
 */
export function removeCurrentRole() {
    localStorage.removeItem(CURRENT_ROLE_CACHE_NAME);
    sessionStorage.removeItem(CURRENT_ROLE_CACHE_NAME);
}
