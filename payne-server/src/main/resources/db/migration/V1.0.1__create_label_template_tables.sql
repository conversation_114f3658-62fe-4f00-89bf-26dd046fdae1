-- 标签模板表
CREATE TABLE LABEL_TEMPLATE (
    ID VARCHAR2(50) PRIMARY KEY,
    TEMPLATE_NAME VARCHAR2(100) NOT NULL,
    TEMPLATE_TYPE VARCHAR2(20) DEFAULT 'CUSTOM',
    LAYOUT_CONFIG CLOB,
    FIELD_MAPPING CLOB,
    COLOR_CONFIG CLOB,
    PAGE_SETTINGS CLOB,
    IS_DEFAULT NUMBER(1) DEFAULT 0,
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    UPDATE_TIME DATE,
    STATUS VARCHAR2(10) DEFAULT 'ACTIVE'
);

-- 彩色标签批量设置表
CREATE TABLE COLOR_LABEL_BATCH (
    ID VARCHAR2(50) PRIMARY KEY,
    BATCH_NAME VARCHAR2(100) NOT NULL,
    COIN_IDS CLOB,
    COLOR_SETTINGS CLOB,
    APPLY_STATUS VARCHAR2(20) DEFAULT 'DRAFT',
    CREATE_USER VARCHAR2(50),
    CREATE_TIME DATE DEFAULT SYSDATE,
    UPDATE_TIME DATE
);

-- 字段定义配置表（用于存储字段显示名称和分类）
CREATE TABLE FIELD_DEFINITION (
    ID VARCHAR2(50) PRIMARY KEY,
    FIELD_NAME VARCHAR2(100) NOT NULL,
    DISPLAY_NAME VARCHAR2(200) NOT NULL,
    FIELD_TYPE VARCHAR2(50),
    CATEGORY VARCHAR2(50),
    SORT_ORDER NUMBER(3) DEFAULT 0,
    IS_ENABLED NUMBER(1) DEFAULT 1,
    CREATE_TIME DATE DEFAULT SYSDATE
);

-- 插入基础字段定义数据
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'specialMark', '特殊标记', 'String', 'GRADE_INFO', 1, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'bankName', '银行名称', 'String', 'BASIC_INFO', 2, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'coinName1', '钱币名称1', 'String', 'BASIC_INFO', 3, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'serialNumber', '钱币编号', 'String', 'BASIC_INFO', 4, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'version', '版别', 'String', 'BASIC_INFO', 5, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'gradeScore', '评级打分', 'String', 'GRADE_INFO', 6, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'yearInfo', '年代', 'String', 'BASIC_INFO', 7, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'coinType', '钱币类型', 'String', 'BASIC_INFO', 8, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'faceValue', '面值', 'String', 'BASIC_INFO', 9, 1, SYSDATE);
INSERT INTO FIELD_DEFINITION VALUES (SYS_GUID(), 'authenticity', '真伪鉴定', 'String', 'GRADE_INFO', 10, 1, SYSDATE);