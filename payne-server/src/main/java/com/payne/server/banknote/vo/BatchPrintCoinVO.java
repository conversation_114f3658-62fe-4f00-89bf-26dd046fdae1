package com.payne.server.banknote.vo;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Map;

/**
 * 批量打印钱币视图对象
 *
 * <AUTHOR>
 * @date 2025-01-12
 */
@Data
public class BatchPrintCoinVO {

    private String id;

    private String diyCode;
    private String serialNumber;
    private String sendformNumber;
    private String authenticity;
    private String coinName;
    private String version;
    private String additionalInfo;
    private String yearInfo;
    private String gradeLevel;
    private String weight;
    private String size;
    private Integer gradeScore;
    private String customerName;
    private BigDecimal fee;
    private Integer auditStatus;
    private String coinType;
    private String bankName;
    private LocalDateTime createTime;
    
    // 自定义模板字段数据
    private Map<String, Object> customFields;

    public Map<String, Object> getCustomFields() {
        return customFields;
    }

    public void setCustomFields(Map<String, Object> customFields) {
        this.customFields = customFields;
    }
}
