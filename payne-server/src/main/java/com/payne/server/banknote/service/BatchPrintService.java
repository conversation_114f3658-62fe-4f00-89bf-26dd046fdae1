package com.payne.server.banknote.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.payne.server.banknote.entity.PjOSendformItem;
import com.payne.server.banknote.vo.BatchPrintCoinVO;
import com.payne.server.banknote.vo.PrintAuditResultVO;
import com.payne.server.banknote.vo.PrintDataVO;

import jakarta.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 批量打印服务接口
 *
 * <AUTHOR>
 * @since 2025-01-12
 */
public interface BatchPrintService {

    /**
     * 转换为VO分页对象
     */
    IPage<BatchPrintCoinVO> convertToVOPage(IPage<PjOSendformItem> page);

    /**
     * 检查审核状态
     */
    PrintAuditResultVO checkAuditStatus(List<String> coinIds);

    /**
     * 根据自定义模板生成打印数据
     */
    PrintDataVO generateCustomTemplatePrintData(List<String> coinIds, String templateId,
                                                Integer conversionType, String printType);

    /**
     * 生成预览数据
     */
    Map<String, Object> generatePreviewData(List<String> coinIds, String templateId, Integer conversionType);

    /**
     * 执行打印
     */
    void executePrint(List<String> coinIds, String templateId, Integer conversionType, String action, HttpServletResponse response);
}
